<template>
	<view class="myCard" @click="gotoDetailFun">
		<view class="cardTopName">单号：{{props.item.order_no}}  {{props.item.out_order_type_name}}</view>
		<view class="cardRow">
			<view>配布日期：</view>
			<view>{{formatDate(props.item.arrange_time, 'Y-M-D')}}</view>
		</view>
		<view class="cardRow">
			<view>进度状态：</view>
			<view class="fzrRight">{{props.item.business_status_name}}</view>
		</view>
		<view class="cardRow">
			<view>客户名称：</view>
			<view>{{props.item.biz_unit_name}}</view>
		</view>
		<view class="cardRow">
			<view>预约单号：</view>
			<view>{{props.item.src_order_no}}</view>
		</view>
		<view class="cardRow">
			<view>销售员：</view>
			<view>{{props.item.sale_user_name}}</view>
		</view>
		<view class="cardRow">
			<view>仓库名称：</view>
			<view>{{props.item.warehouse_name}}</view>
		</view>
		<view class="cardRow">
			<view>配布数量：</view>
			<text class="fzrRight">{{props.item.push_roll/100}}条 {{props.item.push_weight/10000}}KG {{props.item.push_roll/100}}米</text>
		</view>
		<view class="cardRow">
			<view>已配数量：</view>
			<text class="fzrRight">{{props.item.total_roll/100}}条  {{props.item.total_weight/10000}}Kg {{props.item.total_length/100}}米</text>
		</view>
		<view v-if="!props.isDetail && !props.isSelect" class="lookDetail">
			<text>查看详情</text>
			<up-icon name="arrow-right" size="20"></up-icon>
		</view>
	</view>
</template>

<script setup>
	import { formatDate } from '@/common/tool.js'

	// Props 定义
	const props = defineProps({
		item: {
			type: Object,
			default: () => ({})
		},
		index: {
			type: Number,
			default: 0
		},
		isSelect: {
			type: Boolean,
			default: false
		},
		isDetail: {
			type: Boolean,
			default: false
		}
	})

	// 方法定义
	const gotoDetailFun = () => {
		if (props.isSelect) {
			return
		}
		//uni.$bjInfo = props.item;
		console.log("----444->>>>" + props.index);
		/* uni.navigateTo({
			url: '/pages/saleship/salepickscandetail?billid=' + props.index
		}) */
	}
</script>

<style>
	
</style>

