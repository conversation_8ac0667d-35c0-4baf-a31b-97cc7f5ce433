<template>
	<view class="myCard" @click="gotoDetailFun">
		<view class="cardTopName">单号：{{item.order_no}}  {{item.out_order_type_name}}</view>
		<view class="cardRow">
			<view>配布日期：</view>
			<view>{{formatDate(item.arrange_time)}}</view>
		</view>
		<view class="cardRow">
			<view>进度状态：</view>
			<view class="fzrRight">{{item.business_status_name}}</view>
		</view>
		<view class="cardRow">
			<view>客户名称：</view>
			<view>{{item.biz_unit_name}}</view>
		</view>
		<view class="cardRow">
			<view>预约单号：</view>
			<view>{{item.src_order_no}}</view>
		</view>
		<view class="cardRow">
			<view>销售员：</view>
			<view>{{item.sale_user_name}}</view>
		</view>
		<view class="cardRow">
			<view>仓库名称：</view>
			<view>{{item.warehouse_name}}</view>
		</view>
		<view class="cardRow">
			<view>配布数量：</view>
			<text class="fzrRight">{{item.push_roll/100}}条 {{item.push_weight/10000}}KG {{item.push_roll/100}}米</text>
		</view>
		<view class="cardRow">
			<view>已配数量：</view>
			<text class="fzrRight">{{item.total_roll/100}}条  {{item.total_weight/10000}}Kg {{item.total_length/100}}米</text>
		</view>
		<view v-if="!isDetail && !isSelect" class="lookDetail">
			<text>查看详情</text>
			<up-icon name="arrow-right" size="20"></up-icon>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			index: {
				type: Number,
				default: 0
			},
			isSelect: {
				type: Boolean,
				default: false
			},
			isDetail: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				
			}
		},
		methods: {
			// 格式化日期方法
			formatDate(timestamp) {
				if (!timestamp) return '';

				try {
					let date;
					if (typeof timestamp === 'string') {
						// 如果是字符串，尝试解析
						if (timestamp.includes('-') || timestamp.includes('/')) {
							date = new Date(timestamp);
						} else {
							// 如果是纯数字字符串，当作时间戳处理
							const ts = parseInt(timestamp);
							date = new Date(ts < 10000000000 ? ts * 1000 : ts);
						}
					} else if (typeof timestamp === 'number') {
						// 如果是数字，判断是秒还是毫秒
						date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
					} else {
						date = new Date(timestamp);
					}

					// 检查日期是否有效
					if (isNaN(date.getTime())) {
						console.warn('Invalid date:', timestamp);
						return timestamp; // 返回原值
					}

					// 格式化为 yyyy-mm-dd
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');

					return `${year}-${month}-${day}`;
				} catch (error) {
					console.error('Date formatting error:', error);
					return timestamp; // 返回原值
				}
			},

			gotoDetailFun: function() {
				if(this.isSelect) {
					return
				}
				//uni.$bjInfo = this.item;
				console.log("----444->>>>" + this.index);
				/* uni.navigateTo({
					url: '/pages/saleship/salepickscandetail?billid=' + this.index
				}) */
			}
		}
	}
</script>

<style>
	
</style>

