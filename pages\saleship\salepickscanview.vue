<template>
	<view class="myCard" @click="gotoDetailFun">
		<view class="cardTopName">单号：{{item.order_no}}  {{item.out_order_type_name}}</view>
		<view class="cardRow">
			<view>配布日期：</view>
			<view>{{formatDate(item.arrange_time, 'Y-M-D')}}</view>
		</view>
		<view class="cardRow">
			<view>进度状态：</view>
			<view class="fzrRight">{{item.business_status_name}}</view>
		</view>
		<view class="cardRow">
			<view>客户名称：</view>
			<view>{{item.biz_unit_name}}</view>
		</view>
		<view class="cardRow">
			<view>预约单号：</view>
			<view>{{item.src_order_no}}</view>
		</view>
		<view class="cardRow">
			<view>销售员：</view>
			<view>{{item.sale_user_name}}</view>
		</view>
		<view class="cardRow">
			<view>仓库名称：</view>
			<view>{{item.warehouse_name}}</view>
		</view>
		<view class="cardRow">
			<view>配布数量：</view>
			<text class="fzrRight">{{item.push_roll/100}}条 {{item.push_weight/10000}}KG {{item.push_roll/100}}米</text>
		</view>
		<view class="cardRow">
			<view>已配数量：</view>
			<text class="fzrRight">{{item.total_roll/100}}条  {{item.total_weight/10000}}Kg {{item.total_length/100}}米</text>
		</view>
		<view v-if="!isDetail && !isSelect" class="lookDetail">
			<text>查看详情</text>
			<up-icon name="arrow-right" size="20"></up-icon>
		</view>
	</view>
</template>

<script>
	import { formatDate } from '@/common/tool.js'

	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			index: {
				type: Number,
				default: 0
			},
			isSelect: {
				type: Boolean,
				default: false
			},
			isDetail: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {

			}
		},
		computed: {
			// 使用项目已有的格式化方法
			formatDate() {
				return formatDate
			}
		},
		methods: {
			gotoDetailFun: function() {
				if(this.isSelect) {
					return
				}
				//uni.$bjInfo = this.item;
				console.log("----444->>>>" + this.index);
				/* uni.navigateTo({
					url: '/pages/saleship/salepickscandetail?billid=' + this.index
				}) */
			}
		}
	}
</script>

<style>
	
</style>

