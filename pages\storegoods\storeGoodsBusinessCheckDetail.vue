<template>
	<view class="wrap">
		<up-form ref="uForm">
			<up-form-item>
				<text class="title" style="width:200px;">单号：{{goodsCheckBillNo}}</text>
				<text class="title" style="width:200px;">日期：{{goodsCheckBillDate}}</text>
			</up-form-item>
			<up-form-item>
				<text class="title" style="width:200px;">仓库名称：{{storeName}}</text>
				<text class="title" style="width:200px;">仓位名称：{{storeStationName}}</text>
			</up-form-item>
			<up-form-item label-width="150" label="条码资料:">
				<input type="text" v-model="qrBarCode" maxlength="-1" style="width:200px;"
					@confirm="goodsCheckBillDetailScan" />
				<checkbox-group @change="barCodeDelChange">
					<checkbox ref="checkBoxRef" :checked="barCodeDelStatus">删除</checkbox>
				</checkbox-group>
			</up-form-item>
			<up-form-item>
				<text class="title">{{billDataMessage}}</text>
			</up-form-item>
			<up-form-item>
				<text class="title" style="width:200px;">成品编号：{{fabricGoodsNo}}</text>
				<text class="title" style="width:200px;">成品重量：{{goodsQty}}KG</text>
			</up-form-item>
			<up-form-item>
				<text class="title" style="width:200px;">成品色号：{{goodsCodeNo}}</text>
				<text class="title" style="width:200px;">成品颜色：{{goodsCodeName}}</text>
			</up-form-item>
			<up-form-item>
				<text class="title" style="width:200px;">成品缸号：{{crockNo}}</text>
				<text class="title" style="width:200px;">缸号卷号：{{goodsBillNo}}</text>

			</up-form-item>
			<up-form-item>
				<text class="title" style="width:200px;">本架盘前：{{billSumOldRoll}}条</text>
				<text class="title" style="width:200px;">本架实盘：{{billSumNewRoll}}条</text>
			</up-form-item>
			<up-form-item>
				<text class="title" style="width:200px;">本色盘前：{{goodsCodeNoSumOldRoll}}条</text>
				<text class="title" style="width:200px;">本色实盘：{{goodsCodeNoSumNewRoll}}条</text>
			</up-form-item>
			<up-form-item>
				<text class="title" style="width:200px;">本缸盘前：{{crockNoSumOldRoll}}条</text>
				<text class="title" style="width:200px;">本缸实盘：{{crockNoSumNewRoll}}条</text>
			</up-form-item>
		</up-form>
		<view class="u-demo-area">
			<up-toast ref="uToast"></up-toast>
			<wyb-table ref="table" :headers="headersMaster" :contents="goodsDetailList" />
		</view>

		<view class="submitView">
			<up-button type="primary" style="width:100px;" class="commitBtn" :ripple="true" ripple-bg-color="#909399"
				@click="commitBtnFun">
				{{commitType ? '消审' : '审核'}}
			</up-button>
		</view>
	</view>
</template>

<script setup>
	import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
	import { onLoad, onShow, onHide } from '@dcloudio/uni-app'
	import { formatDate } from '@/common/tool.js'
	import util from '@/common/util'

	// 响应式数据
	const borderColor = ref('#e4e7ed')
	const align = ref('center')
	const index = ref(0)
	const commitType = ref('')
	const commitProcName = ref('')
	const actionSheetShow = ref(false)
	const qrBarCode = ref('')
	const goodsCheckBillID = ref(0)
	const goodsCheckBillNo = ref('')
	const goodsCheckBillDate = ref('')
	const goodsCheckBillMasterNo = ref('')
	const storeNameID = ref('')
	const storeName = ref('')
	const storeStationID = ref('')
	const storeStationNo = ref('')
	const storeStationName = ref('')
	const customerName = ref('')
	const fabricGoodsNo = ref('')
	const fabricGoodsName = ref('')
	const goodsCodeNo = ref('')
	const goodsCodeName = ref('')
	const crockNo = ref('')
	const goodsBillNo = ref('')
	const goodsQty = ref(0)
	const storeStationNameFocus = ref(false)
	const billSumOldRoll = ref(0)
	const billSumNewRoll = ref(0)
	const goodsCodeNoSumOldRoll = ref(0)
	const goodsCodeNoSumNewRoll = ref(0)
	const crockNoSumOldRoll = ref(0)
	const crockNoSumNewRoll = ref(0)
	const barCodeDelStatus = ref(false)
	const goodsDetailList = ref([])
	const billDataMessage = ref('')
	const headersMaster = ref([
		{
			label: '成品编号',
			key: 'product_code'
		},
		{
			label: '成品名称',
			key: 'product_name'
		},
		{
			label: '成品色号',
			key: 'product_color_code'
		},
		{
			label: '成品颜色',
			key: 'product_color_name'
		},
		{
			label: '成品缸号',
			key: 'dyelot_number'
		},
		{
			label: '盘前条数',
			key: 'roll'
		},
		{
			label: '实盘条数',
			key: 'check_roll'
		},
		{
			label: '盈亏条数',
			key: 'different_roll'
		}
	])
	const scanReceiver = ref(null)
	const isPageActive = ref(false) // 添加页面活动状态标志

	// 生命周期钩子
	onLoad((e) => {
		if (e.billid) {
			goodsCheckBillID.value = e.billid;
			goodsCheckBillDetailData();
		}

		// #ifdef APP-PLUS
		isPageActive.value = true;
		registerScanBroadcast();
		// #endif
	})

	onUnmounted(() => {
		// #ifdef APP-PLUS
		isPageActive.value = false;
		// unregisterBroadcast();
		// #endif
	})

	onHide(() => {
		// 页面隐藏时
		isPageActive.value = false;
	})

	onShow(() => {
		// 页面显示时
		isPageActive.value = true;
	})

	// 方法定义
	// 添加通用错误提示方法
	const showError = (message) => {
		playError();
		uni.showModal({
			title: '提示',
			content: message,
			showCancel: false
		});
	}

	// 注册扫码广播接收器
	const registerScanBroadcast = () => {
		try {
			const main = plus.android.runtimeMainActivity();

			// 先配置扫码枪广播设置
			try {
				const Intent = plus.android.importClass("android.content.Intent");
				const intent = new Intent("com.android.scanner.service_settings");
				intent.putExtra(
					"action_barcode_broadcast",
					"com.android.server.scannerservice.broadcast"
				);
				intent.putExtra("key_barcode_broadcast", "scannerdata");
				main.sendBroadcast(intent);
			} catch (error) {
				console.error("配置扫码枪广播失败：", error);
			}

			// 注册广播接收器
			const IntentFilter = plus.android.importClass(
				"android.content.IntentFilter"
			);
			const filter = new IntentFilter();
			filter.addAction("com.android.server.scannerservice.broadcast");
			console.log("添加广播action完成");

			const receiver = plus.android.implements(
				"io.dcloud.feature.internal.reflect.BroadcastReceiver",
				{
					onReceive: (context, intent) => {
						// 只有当页面活动时才处理广播
						if (!isPageActive.value) return;

						try {
							const scanResult = intent.getStringExtra("scannerdata");
							console.log("配布单详情-扫码结果:", scanResult);
							if (scanResult) {
								qrBarCode.value = scanResult;
								nextTick(() => {
									goodsCheckBillDetailScan();
								});
							}
						} catch (error) {
							console.error("处理广播数据时出错：", error);
						}
					},
				}
			);

			// 注册广播接收器
			main.registerReceiver(receiver, filter);
			scanReceiver.value = receiver;
			console.log("扫码广播注册成功，等待扫码...");
		} catch (error) {
			console.error("注册扫码广播失败：", error);
			console.error("错误详情：", error.message);
			console.error("错误堆栈：", error.stack);
		}
	}

	// 注销扫码广播接收器
	const unregisterScanBroadcast = () => {
		if (scanReceiver.value) {
			try {
				const main = plus.android.runtimeMainActivity();
				main.unregisterReceiver(scanReceiver.value);
				scanReceiver.value = null;
				console.log("扫码广播注销成功");
			} catch (error) {
				console.error("注销扫码广播失败：", error);
			}
		}
	}

	const playSuccess = () => {
		util.playSuccessAudio();
	}

	const playError = () => {
		util.playErrorAudio();
	}

	const barCodeDelChange = () => {
		barCodeDelStatus.value = !barCodeDelStatus.value;
	}

	const goodsCheckBillDetailScan = () => {
		if (storeNameID.value == 0 && goodsCheckBillNo.value == '') {
			qrBarCode.value = '';
			showError('请先新增单据');
			return;
		}
		console.log("qrBarCode ---->>" + qrBarCode.value);

		var aQRBarCode = '';
		var aBarCode = '';
		if (qrBarCode.value.startsWith('66^') > 0) {
			aQRBarCode = qrBarCode.value
		} else {
			aBarCode = qrBarCode.value;
		}

		if (aQRBarCode == '' && aBarCode == '') {
			qrBarCode.value = '';
			showError('请扫描二维码或者条码');
			return;
		}

		let aBarCodeDelStatus = 1
		if (barCodeDelStatus.value) {
			aBarCodeDelStatus = 3;
		};

		util.request({
			url: '/product/productCheckOrder/updateProductCheckOrder',
			method: 'put',
			data: {
				'arrange_type': aBarCodeDelStatus,
				'id': parseInt(goodsCheckBillID.value),
				'bar_code': aBarCode,
				'qr_code': aQRBarCode,
			},
			success: (res) => {
				console.log("-->>--" + JSON.stringify(res.data));
				if (res.data.code == '0') {
					let aResultData = res.data.data;
					fabricGoodsNo.value = aResultData.product_code;
					fabricGoodsName.value = aResultData.product_name;
					goodsCodeNo.value = aResultData.product_color_code;
					goodsCodeName.value = aResultData.product_color_name;
					crockNo.value = aResultData.dyelot_number;
					goodsBillNo.value = aResultData.volume_number;
					goodsQty.value = aResultData.weight / 10000;
					billSumOldRoll.value = aResultData.warehouse_bin_check_before_roll / 100;
					billSumNewRoll.value = aResultData.warehouse_bin_check_roll / 100;
					goodsCodeNoSumOldRoll.value = aResultData.color_check_before_roll / 100;
					goodsCodeNoSumNewRoll.value = aResultData.color_check_roll / 100;
					crockNoSumOldRoll.value = aResultData.dye_check_before_roll / 100;
					crockNoSumNewRoll.value = aResultData.dye_check_roll / 100;

					playSuccess();
					goodsCheckBillDetailData();
					billDataMessage.value = res.data.msg;
					qrBarCode.value = '';

				} else {
					showError(res.data.msg);
					qrBarCode.value = '';
				}
			},
			fail: (error) => {
				showError('连接服务器出错，请检查后台服务是否启动！');
				console.log('error', error)
			},
		})
	}

	const goodsCheckBillDetailData = () => {
		console.log("--aaa->>" + goodsCheckBillID.value);
		util.request({
			url: '/product/productCheckOrder/getProductCheckOrder',
			method: 'get',
			data: {
				'id': parseInt(goodsCheckBillID.value),
			},
			success: (res) => {
				console.log("-->>--" + JSON.stringify(res.data.data));
				goodsDetailList.value = [];
				goodsCheckBillNo.value = res.data.data.order_no;
				storeName.value = res.data.data.warehouse_name
				storeStationName.value = res.data.data.warehouse_bin_name;
				goodsCheckBillDate.value = formatDate(res.data.data.check_time, 'Y-M-D')

				var aResultData = res.data.data.item_data;
				billSumOldRoll.value = 0;
				billSumNewRoll.value = 0;
				for (var i = 0; i < aResultData.length; i++) {
					aResultData[i].roll = aResultData[i].roll / 100;
					aResultData[i].check_roll = aResultData[i].check_roll / 100;
					aResultData[i].different_roll = aResultData[i].different_roll / 100;

					billSumOldRoll.value = billSumOldRoll.value + aResultData[i].roll;
					billSumNewRoll.value = billSumNewRoll.value + aResultData[i].check_roll;
				};

				goodsDetailList.value = aResultData;
			},
		})
	}

	// 审核按钮方法
	const commitBtnFun = () => {
		if (goodsCheckBillID.value <= 0) {
			showError('当前单据未提交，不能审核或消审！');
			return;
		}

		var aCommitRecallName = '审核';
		if (commitType.value == '') {
			aCommitRecallName = '审核';
		} else {
			aCommitRecallName = '消审';
		}

		console.log('GoodsCheckBillID', goodsCheckBillID.value)
		util.request({
			url: '/product/productCheckOrder/updateProductCheckOrderAuditStatusPass',
			method: 'put',
			data: {
				'id': parseInt(goodsCheckBillID.value),
			},
			success: (res) => {
				console.log('审核', res)
				if (res.data.code == '0') {
					var aResultData = JSON.parse(res.data.data);
					console.log('aResultData', aResultData)
					if (aResultData.code == '0' && aResultData.msg == 'success') {
						playSuccess();
						billDataMessage.value = aCommitRecallName + "成功！";

						if (aCommitRecallName == '审核') {
							commitType.value = '已审核'
						} else {
							commitType.value = ''
						}
					} else {
						showError(aCommitRecallName + '出错！' + aResultData.BillDataMessage);
						return;
					}
				} else {
					showError(aCommitRecallName + '出错，' + res.data.msg);
					return;
				}
			},
			fail: (error) => {
				showError('连接服务器出错，请检查后台服务是否启动！');
			},
		})
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
		padding-bottom: 260rpx;
	}

	.u-radio {
		width: 200rpx !important;
	}

	.submitView {
		width: 100%;
		padding: 16rpx 0 26rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-top: 1rpx solid #f1f1f1;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}

	.submitBtn {
		width: 666rpx;
	}

	.productBox {
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 26rpx 0;
	}

	.tjcpName {
		width: 686rpx;
		height: 40rpx;
		font-size: 16px;
		font-weight: bold;
		border-left: 6rpx solid #007AFF;
		padding-left: 12rpx;
		margin-left: 26rpx;
		margin-top: 26rpx;
	}

	.cpInput {
		width: 150rpx !important;
		margin-right: 12rpx;
	}

	.cpInput>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.cpInput1 {
		width: 200rpx !important;
		margin-right: 12rpx;
	}

	.cpInput1>input {
		box-sizing: border-box;
		border: 1rpx solid #DDDDDD;
		width: 100%;
		height: 60rpx;
		border-radius: 10rpx;
		padding: 0 10rpx;
	}

	.clearIcon {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
	}

	.greenPrice {
		font-size: 16px;
		color: #19BE6B !important;
		font-weight: bold;
	}

	.disFlex {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.inputName {
		color: #ADADAD;
		font-size: 16px;
	}

	.addHKQS {
		display: flex;
		align-items: center;
		padding: 16rpx 26rpx;
		font-size: 15px;
		font-weight: bold;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
