<template>
	<view>
		<button type="primary" @click="onShowDatePicker('range')">单据日期 {{range[0]}} 至 {{range[1]}}</button>
		<view class="u-demo-block">
			<view class="u-page__tag-item">
				<up-search v-model="searchBillNo" :show-action="false" @custom="onTabChange" @search="onTabChange"
					placeholder="请输入单号或往来单位">
				</up-search>
			</view>
		</view>

		<up-sticky>
			<div style="height: 43px; border-bottom: 1rpx solid #eeeeee; background-color: #FFFFFF;">
				<up-tabs :list="tabList" name="title" :activeStyle="{color: 'red'}" :is-scroll="false" :current="activeTabIndex"
					@change="onTabChange"></up-tabs>
			</div>
		</up-sticky>
		<dataNull v-if="list.length == 0" src="/static/img/chahua/gjNull.png" title="暂无相关出仓资料" title1="请添加或者更换搜索添加">
		</dataNull>

		<scroll-view v-else scroll-y="true" :style="{height: scrollHeight}" @scrolltolower="selectDataFun"
			refresher-enabled :refresher-threshold="200" :refresher-triggered="triggered" refresher-background="gray"
			@refresherrefresh="onRefresh" @refresherrestore="onRestore">
			<view v-for="(item, index) in list" :key="index" @click="cardClickFun(item, index)">
				<GoodsBusinessCheckView :item="item" :isSelect="isSelect" :index="index"></GoodsBusinessCheckView>
			</view>
			<getMore :isMore="isMore"></getMore>
		</scroll-view>

		<addBtn url="./storeGoodsBusinessCheckAdd"></addBtn>
		<mx-date-picker :show="showPicker" :type="billDateType" :value="billDateValue" :show-tips="true"
			:begin-text="'开始'" :end-text="'结束'" :show-seconds="false" @confirm="billDateConfirm"
			@cancel="billDateConfirm" />

	</view>
</template>

<script setup>
	import { ref, reactive, onMounted, watch, nextTick } from 'vue'
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import { formatDate } from '@/common/tool.js'
	import addBtn from '@/components/addBtn/addBtn.vue'
	import GoodsBusinessCheckView from '@/pages/storegoods/storeGoodsBusinessCheckView.vue'
	import util from '../../common/util'
	import MxDatePicker from "@/components/mx-datepicker/mx-datepicker.vue"
	import dataNull from '@/components/dataNull/dataNull.vue'
	import getMore from '@/components/getMore/getMore.vue'

	// 响应式数据
	const activeTabIndex = ref(0)
	const list = ref([])
	const pageIndex = ref(1)
	const isMore = ref(true)
	const scrollHeight = ref('667px')
	const triggered = ref(false)
	const isSelect = ref(false)
	const searchBillNo = ref('')
	const showPicker = ref(false)
	const billBeginDate = ref(formatDate(new Date(), 'Y-M-D'))
	const billEndDate = ref(formatDate(new Date(), 'Y-M-D'))
	const range = ref([formatDate(new Date(), 'Y-M-D'), formatDate(new Date(), 'Y-M-D')])
	const billDateType = ref('range')
	const billDateValue = ref('')
	const tabList = ref([
		{
			name: '未审核',
			status: '0'
		},
		{
			name: '已审核',
			status: '1'
		}
	])

	// 生命周期钩子
	onLoad((e) => {
		uni.getSystemInfo({
			success(res) {
				scrollHeight.value = res.windowHeight - 40 + 'px';
			}
		})
		selectDataFun();
	})

	// 监听器
	watch(activeTabIndex, (newVal, oldVal) => {
		selectDataFun(newVal);
	}, { deep: true })

	// 方法定义
	const onTabChange = (index) => {
		activeTabIndex.value = index;
	}

	const onShowDatePicker = (type) => { //显示
		billDateType.value = type;
		showPicker.value = true;
		billDateValue.value = range.value;
	}

	const billDateConfirm = (e) => { //选择
		showPicker.value = false;
		if (e) {
			billBeginDate.value = formatDate(Date.parse(e.value[0]), 'Y-M-D');
			billEndDate.value = formatDate(Date.parse(e.value[1]), 'Y-M-D');
			range.value = [billBeginDate.value, billEndDate.value];
			selectDataFun();
		}
	}

	const selectDataFun = () => {
		if (activeTabIndex.value == 0) {
			util.request({
				url: '/product/productCheckOrder/getProductCheckOrderList',
				data: {
					'audit_status': '1',
					method: 'GET',
				},
				success: (res) => {
					triggered.value = false;
					list.value = [];
					let data = res.data.data.list;
					if (res.data.data.total != 0) {
						if (pageIndex.value == 1) {
							list.value = [];
						}
						if (res.data == 10) {
							pageIndex.value += 1;
							isMore.value = true;
						} else {
							isMore.value = false;
						}
						list.value = list.value.concat(data);
					}
				},
			})
		} else if (activeTabIndex.value == 1) {
			util.request({
				url: '/product/productCheckOrder/getProductCheckOrderList',
				data: {
					'audit_status': '2',
					'start_check_time': billBeginDate.value,
					'end_check_time': billEndDate.value,
					method: 'GET',
				},
				success: (res) => {
					console.log("-->>--" + JSON.stringify(res.data.data));
					triggered.value = false;
					list.value = [];
					let data = res.data.data.list;

					if (res.data.data.total != 0) {
						if (pageIndex.value == 1) {
							list.value = [];
						}
						if (res.data == 10) {
							pageIndex.value += 1;
							isMore.value = true;
						} else {
							isMore.value = false;
						}
						list.value = list.value.concat(data);
					}
				},
			})
		}
	}

	// 卡片点击方法
	const cardClickFun = (item, index) => {
		uni.navigateTo({
			url: '/pages/storegoods/storeGoodsBusinessCheckDetail?billid=' + item.id
		})
	}

	// 下拉刷新
	const onRefresh = () => {
		if (triggered.value) return
		triggered.value = true;
		cxGetDataFun();
	}

	const onRestore = (e) => {
		triggered.value = false; // 需要重置
	}

	// 重新获取数据
	const cxGetDataFun = () => {
		pageIndex.value = 1;
		isMore.value = true;
		selectDataFun();
	}
</script>

<style>
	page {
		background-color: #F8F8F8;
	}

	button {
		margin: 20upx;
		font-size: 28upx;
	}
</style>
