<template>
	<view class="wrap yjfk-form-right">
		<up-form labelWidth="100" class="form" :model="model" :rules="rules" ref="uForm" label-position="top">
			<up-form-item label="问题和意见" prop="content">
				<u-input type="textarea" placeholder="请填写10个字以上的问题描述以便我们提供更好的帮助"
						v-model="model.content" height="200" maxlength="500" />
			</up-form-item>
			<up-form-item label="联系方式（手机、邮箱、QQ号码）" prop="contact">
				<u-input class="input-left-form" placeholder="选填，便于我们与你联系，进一步沟通"
						v-modtel="model.contact" type="text" maxlength="200"></u-input>
			</up-form-item>
		</up-form>
		<view class="form-footer">
			<up-button class="btn" type="primary" @click="submit">提交</up-button>
			<!-- <u-button class="btn" type="default" @click="cancel">关闭</u-button> -->
		</view>
	</view>
</template>
<script>
/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
export default {
	data() {
		return {
			model: {
				id: '',
				category: '',
				content: '',
				contact: '',
				deviceInfo: ''
			},
			rules: {
				category: [
					{
						required: true,
						message: '请选择问题和意见的分类',
						trigger: ['change','blur'],
					}
				],
				content: [
					{
						required: true,
						min: 10, max: 500,
						message: '问题和意见在 10 到 500 个字符之间',
						trigger: ['change','blur'],
					}
				],
			}
		};
	},
	onReady() {
		this.$refs.uForm.setRules(this.rules);
		// 获取设备信息
		uni.getSystemInfo({
			success: res => {
				this.model.deviceInfo = JSON.stringify(res);
			}
		});
	},
	methods: {
		submit() {
			 console.log(this.model)
			/*
			this.$refs.uForm.validate(valid => {
				if (valid) {
					
					this.$u.api.commentSave(this.model).then(res => {
						uni.showModal({
							title: '提示',
							content: res.message,
							showCancel: false,
							success: function () {
								if (res.result == 'true') {
									uni.navigateBack();
								}
							}
						});
					});
					
				} else {
					this.$u.toast('您填写的信息有误，请根据提示修正。');
				}
			});*/
		},
		cancel() {
			uni.navigateBack();
		}
	}
};
</script>
<style lang="scss" scoped>
.wrap{
	background-color: white;
}
.input-placeholder{
	text-align: right;
}

</style>
