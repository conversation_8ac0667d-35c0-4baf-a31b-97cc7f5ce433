<template>
  <view>
    <!-- 标签页 -->
    <up-sticky>
      <div
        style="
          height: 43px;
          border-bottom: 1rpx solid #eeeeee;
          background-color: #ffffff;
        "
      >
        <up-tabs
          :list="tabList"
          :current="activeTabIndex"
          @change="onTabChange"
          :activeStyle="{color: 'red'}"
          :scrollable="false"
        ></up-tabs>
      </div>
    </up-sticky>

    <!-- 搜索框 -->
    <view class="search-box">
      <up-search
        v-model="searchKeyword"
        placeholder="请输入配布单号"
        :showAction="false"
        @change="onSearch"
        @custom="onSearch"
        @search="onSearch"
      ></up-search>
    </view>

    <!-- 内容展示区 -->
    <view class="content">
      <!-- 空状态展示 -->
      <view v-if="showEmpty" class="empty-state">
        <dataNull
          src="/static/img/chahua/gjNull.png"
          title="暂无相关配布单"
          title1="请添加或者更换搜索添加"
        ></dataNull>
      </view>

      <!-- 列表展示 -->
      <scroll-view
        v-else
        scroll-y="true"
        :style="{ height: 'calc(100vh - 120px)' }"
        @scrolltolower="onLoadMore"
        refresher-enabled
        :refresher-triggered="triggered"
        @refresherrefresh="onRefresh"
        @refresherrestore="onRestore"
        @refresherpulling="onPulling"
        :refresher-threshold="45"
        refresher-default-style="black"
        :refresher-background="'#f8f8f8'"
      >
        <view
          v-for="(item, index) in list"
          :key="index"
          @click="cardClickFun(item, index)"
        >
          <salepickscanview
            :item="item"
            :isSelect="isSelect"
            :index="index"
          ></salepickscanview>
        </view>

        <!-- 加载更多提示 -->
        <view class="load-more" v-if="list.length > 0">
          {{ hasMore ? "正在加载更多..." : "没有更多数据了" }}
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { useStore } from 'vuex'
import dataNull from "@/components/dataNull/dataNull.vue"
import salepickscanview from "@/pages/saleship/salepickscanview.vue"

// 获取store实例
const store = useStore()

// 响应式数据
const isSelect = ref(false)

// tab列表
const tabList = ref([
  { name: '未配', status: '1,2' },    // 未配：1待配布,2配布中
  { name: '已配', status: '3,4' },    // 已配：3已配布,4待出仓
  { name: '全部', status: '1,2,3,4' } // 全部：所有状态
])

// 当前激活的tab索引
const activeTabIndex = ref(0)

// 搜索关键词
const searchKeyword = ref("")

// 列表数据
const list = ref([])

// 加载状态
const loading = ref(false)
const pageIndex = ref(1)
const pageSize = ref(15)
const hasMore = ref(true)
const triggered = ref(false)
const isRefreshing = ref(false)
const searchTimer = ref(null) // 添加防抖定时器

// 计算属性
const showEmpty = computed(() => {
  return !loading.value && list.value.length === 0
})

// 获取数据
const getData = async (isLoadMore = false) => {
  console.log('u',uni.$u.api)

  // 🔍 调试信息：检查token状态
  // 使用正确的方式访问store
  const currentToken = store?.state?.vuex_token || '';
  const apiUrl = store?.state?.apiurl || '';

  const remoteTokenData = uni.getStorageSync('RemoteTokenData');
  const userTokenData = uni.getStorageSync('userToken');
  const lifeData = uni.getStorageSync('lifeData');

  console.log('🔑 当前Vuex token:', currentToken);
  console.log('💾 本地存储RemoteTokenData:', remoteTokenData);
  console.log('💾 本地存储userToken:', userTokenData);
  console.log('💾 本地存储lifeData:', lifeData);
  console.log('🌐 API URL:', apiUrl);
  console.log('🏪 Store对象:', store);
  console.log('🏪 完整store状态:', store?.state);

  // 如果Vuex中没有token，但本地存储有，则同步到Vuex
  if (!currentToken && remoteTokenData?.data?.token) {
    console.log('🔄 同步token到Vuex...');
    try {
      // 使用原生store方法
      store.commit('$uStore', {
        name: 'vuex_token',
        value: remoteTokenData.data.token
      });
      console.log('✅ 同步token到Vuex成功:', remoteTokenData.data.token);
    } catch (error) {
      console.error('❌ 同步token失败:', error);
    }
  }

  // 如果仍然没有token，提示用户重新登录
  if (!currentToken && !remoteTokenData?.token && !userTokenData?.Token) {
    console.warn('⚠️ 没有找到token，可能需要重新登录');
    uni.showModal({
      title: '提示',
      content: '登录状态已失效，请重新登录',
      showCancel: true,
      cancelText: '手动设置',
      confirmText: '重新登录',
      success: (res) => {
        if (res.confirm) {
          uni.reLaunch({
            url: '/pages/sys/login/index'
          });
        } else if (res.cancel) {
          // 手动设置token（临时调试用）
          uni.showModal({
            title: '手动设置Token',
            content: '请先登录获取token，或联系管理员',
            success: () => {
              uni.reLaunch({
                url: '/pages/sys/login/index'
              });
            }
          });
        }
      }
    });
    return;
  }

  // 尝试从其他存储位置恢复token
  if (!currentToken) {
    let tokenToUse = null;
    if (remoteTokenData?.data?.token) {
      tokenToUse = remoteTokenData.data.token;
    } else if (userTokenData?.Token) {
      tokenToUse = userTokenData.Token;
    }

    if (tokenToUse) {
      console.log('🔄 从本地存储恢复token:', tokenToUse);
      try {
        store.commit('$uStore', {
          name: 'vuex_token',
          value: tokenToUse
        });
        console.log('✅ 恢复token成功');
      } catch (error) {
        console.error('❌ 恢复token失败:', error);
      }
    }
  }

  uni.$u.api.getFpmArrangeOrderList({
    order_no: searchKeyword.value,
    src_order_no: searchKeyword.value,
    business_status_ids: tabList.value[activeTabIndex.value].status, // 根据tab传递对应的状态组合
    page: pageIndex.value,
    size: pageSize.value,
  }).then(res => {
    console.log(res)
    const newList = res.list || []
    hasMore.value = newList.length === pageSize.value
    list.value = isLoadMore ? [...list.value, ...newList] : newList
    console.log('newList', newList)
  }).catch(e => {
    console.log('❌ 请求失败:', e)
    uni.showToast({
      title: e.message || e.msg || '请求失败',
      icon: "none",
    })
  }).finally(() => {
    console.log('finish')
    loading.value = false
    uni.hideLoading()
    if (!isLoadMore) {
      setTimeout(() => {
        triggered.value = false
        isRefreshing.value = false
      }, 500)
    }
  })
}

// 下拉刷新
const onRefresh = async () => {
  if (isRefreshing.value) return

  isRefreshing.value = true
  triggered.value = true
  pageIndex.value = 1
  hasMore.value = true

  try {
    await getData(false)
  } catch (error) {
    uni.showToast({
      title: "刷新失败",
      icon: "none",
    })
  }
}

// 加载更多
const onLoadMore = () => {
  if (loading.value || !hasMore.value) return
  pageIndex.value += 1
  loadData(true)
}

// tab切换
const onTabChange = (index) => {
  activeTabIndex.value = index
  searchKeyword.value = ""
  pageIndex.value = 1
  hasMore.value = true
  list.value = []
  loadData()
}

// 搜索防抖处理
const onSearch = () => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  searchTimer.value = setTimeout(() => {
    pageIndex.value = 1
    hasMore.value = true
    list.value = []
    loadData()
  }, 300) // 300ms 的防抖延迟
}

// 加载数据
const loadData = (isLoadMore = false) => {
  if (loading.value) return

  loading.value = true
  if (!isLoadMore) {
    uni.showLoading({
      title: "加载中...",
      mask: false,
    })
  }

  getData(isLoadMore)
}

// 点击卡片
const cardClickFun = (item) => {
  uni.navigateTo({
    url: `/pages/saleship/salepickscandetail?billid=${item.id}&order_no=${item.order_no}`,
  })
}

const onPulling = () => {}
const onRestore = () => {
  triggered.value = false
  isRefreshing.value = false
}

// 生命周期
onMounted(() => {
  loadData()
})

// 组件销毁时清理定时器
onUnmounted(() => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }
})
</script>

<style lang="scss">
page {
  background-color: #f8f8f8;
}

.search-box {
  padding: 10px;
  background-color: #ffffff;
}

.content {
  padding: 5px 0px 10px 0px;
  min-height: calc(100vh - 120px);
}

.content text {
  display: block;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
}

.load-more {
  text-align: center;
  padding: 15px 0;
  color: #999;
  font-size: 14px;
}
</style>
