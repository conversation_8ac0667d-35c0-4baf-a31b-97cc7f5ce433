<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item label-width="80" label="条码:">
				<input type="text" v-model="QRBarCodeData.QRBarCode" :focus="PositionFocus" style="width:300px;"
					@confirm="QRBarCodeReviewScan" />
			</u-form-item>
			<u-form-item>
				<text class="title">{{BillDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:150px;">缸号：{{QRBarCodeData.CrockNo}}</text>
				<text class="title" style="width:100px;">卷号：{{QRBarCodeData.GoodsBillNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:180px;">编号：{{QRBarCodeData.FabricGoodsNo}}</text>
				<text class="title" style="width:150px;">重量：{{QRBarCodeData.Qty}} KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:250px;">品名：{{QRBarCodeData.FabricGoodsName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:250px;">颜色：{{QRBarCodeData.GoodsCodeName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:250px;">订单号：{{QRBarCodeData.SaleOrderNo}}</text>
			</u-form-item>
		</u-form>
	</view>
</template>
<script>
	import util, {
		GetGoodsQRBarCode,
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	export default {
		data() {
			return {
				PositionFocus: true,
				QRBarCodeData: {
					QRBarCode: '',
					FabricGoodsNo: '',
					FabricGoodsName: '',
					GoodsCodeName: '',
					SaleOrderNo: '',
					CrockNo: '',
					GoodsBillNo: '',
					Qty: ''
				},
				BillDataMessage: '',
			};
		},
		methods: {
			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			QRBarCodeReviewScan() {
				var aStrBarCode = GetGoodsQRBarCode(this.QRBarCodeData.QRBarCode);
				aStrBarCode = aStrBarCode.trimRight().trimLeft();
				
				if (aStrBarCode.split("^").length >= 8) {
					console.log("---->>>" + aStrBarCode.split("^")[0]);
					this.QRBarCodeData.CrockNo = aStrBarCode.split("^")[0];
					this.QRBarCodeData.GoodsBillNo = aStrBarCode.split("^")[1];
					this.QRBarCodeData.FabricGoodsNo = aStrBarCode.split("^")[3];
					this.QRBarCodeData.FabricGoodsName = aStrBarCode.split("^")[4];
					this.QRBarCodeData.GoodsCodeName = aStrBarCode.split("^")[5] + '#' + aStrBarCode.split("^")[6];				
					this.QRBarCodeData.SaleOrderNo = aStrBarCode.split("^")[7];
					this.QRBarCodeData.Qty = aStrBarCode.split("^")[8];
					
					util.playSuccessAudio();
					this.QRBarCodeData.QRBarCode = '';
					this.PositionFocus = false;
					this.$nextTick(() => {
						this.PositionFocus = true;
					});
					this.BillDataMessage = '标签条码检测成功！';
					setTimeout(function() {
						uni.hideLoading();
					}, 5000);
				} else {
					util.playErrorAudio();
					this.QRBarCodeData.QRBarCode = '';
					this.PositionFocus = false;
					this.$nextTick(() => {
						this.PositionFocus = true;
					});
					this.BillDataMessage = '标签条码检测出错，请确认！';
					setTimeout(function() {
						uni.hideLoading();
					}, 5000);
				}
			}
		},
	}
</script>
<style lang="scss" scoped>
	page {
		background-color: #f5f5f5;
	}

	.wrap .search {
		background: #ffffff;
	}

	.apply-text {
		font-size: 28rpx;
		color: #333333;

		span {
			color: #999999;
		}
	}

	.user-images {
		width: 28px;
		height: 28px;
		margin-right: 8px;
	}

	.apply-list-foot {
		font-size: 28rpx;
	}

	.personnel-list {
		display: flex;
		align-items: center;
		flex-wrap: wrap;

		.personnel-user {
			position: relative;
			margin: 5px 9px 0;
		}

		.user-images {
			width: 48px;
			height: 48px;
			margin-right: 0;

		}

		.iconfont {
			position: absolute;
			top: -12px;
			right: -5px;
			color: #FE0100;
		}
	}
</style>
