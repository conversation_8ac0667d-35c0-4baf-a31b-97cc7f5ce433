<template>
	<view class="wrap">
		<u-form ref="uForm">
			<u-form-item label-width="80" label="条码:">
				<input type="text" v-model="qrBarCodeData.qrBarCode" :focus="positionFocus" style="width:300px;"
					@confirm="qrBarCodeReviewScan" />
			</u-form-item>
			<u-form-item>
				<text class="title">{{billDataMessage}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:150px;">缸号：{{qrBarCodeData.crockNo}}</text>
				<text class="title" style="width:100px;">卷号：{{qrBarCodeData.goodsBillNo}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:180px;">编号：{{qrBarCodeData.fabricGoodsNo}}</text>
				<text class="title" style="width:150px;">重量：{{qrBarCodeData.qty}} KG</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:250px;">品名：{{qrBarCodeData.fabricGoodsName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:250px;">颜色：{{qrBarCodeData.goodsCodeName}}</text>
			</u-form-item>
			<u-form-item>
				<text class="title" style="width:250px;">订单号：{{qrBarCodeData.saleOrderNo}}</text>
			</u-form-item>
		</u-form>
	</view>
</template>
<script setup>
	import { ref, reactive, nextTick } from 'vue'
	import util, {
		GetGoodsQRBarCode,
	} from '@/common/util';

	// 响应式数据
	const positionFocus = ref(true)
	const qrBarCodeData = reactive({
		qrBarCode: '',
		fabricGoodsNo: '',
		fabricGoodsName: '',
		goodsCodeName: '',
		saleOrderNo: '',
		crockNo: '',
		goodsBillNo: '',
		qty: ''
	})
	const billDataMessage = ref('')

	// 方法定义
	const playSuccess = () => {
		util.playSuccessAudio();
	}

	const playError = () => {
		util.playErrorAudio();
	}

	const qrBarCodeReviewScan = () => {
		var aStrBarCode = GetGoodsQRBarCode(qrBarCodeData.qrBarCode);
		aStrBarCode = aStrBarCode.trimRight().trimLeft();

		if (aStrBarCode.split("^").length >= 8) {
			console.log("---->>>" + aStrBarCode.split("^")[0]);
			qrBarCodeData.crockNo = aStrBarCode.split("^")[0];
			qrBarCodeData.goodsBillNo = aStrBarCode.split("^")[1];
			qrBarCodeData.fabricGoodsNo = aStrBarCode.split("^")[3];
			qrBarCodeData.fabricGoodsName = aStrBarCode.split("^")[4];
			qrBarCodeData.goodsCodeName = aStrBarCode.split("^")[5] + '#' + aStrBarCode.split("^")[6];
			qrBarCodeData.saleOrderNo = aStrBarCode.split("^")[7];
			qrBarCodeData.qty = aStrBarCode.split("^")[8];

			util.playSuccessAudio();
			qrBarCodeData.qrBarCode = '';
			positionFocus.value = false;
			nextTick(() => {
				positionFocus.value = true;
			});
			billDataMessage.value = '标签条码检测成功！';
			setTimeout(function() {
				uni.hideLoading();
			}, 5000);
		} else {
			util.playErrorAudio();
			qrBarCodeData.qrBarCode = '';
			positionFocus.value = false;
			nextTick(() => {
				positionFocus.value = true;
			});
			billDataMessage.value = '标签条码检测出错，请确认！';
			setTimeout(function() {
				uni.hideLoading();
			}, 5000);
		}
	}
</script>
<style lang="scss" scoped>
	page {
		background-color: #f5f5f5;
	}

	.wrap .search {
		background: #ffffff;
	}

	.apply-text {
		font-size: 28rpx;
		color: #333333;

		span {
			color: #999999;
		}
	}

	.user-images {
		width: 28px;
		height: 28px;
		margin-right: 8px;
	}

	.apply-list-foot {
		font-size: 28rpx;
	}

	.personnel-list {
		display: flex;
		align-items: center;
		flex-wrap: wrap;

		.personnel-user {
			position: relative;
			margin: 5px 9px 0;
		}

		.user-images {
			width: 48px;
			height: 48px;
			margin-right: 0;

		}

		.iconfont {
			position: absolute;
			top: -12px;
			right: -5px;
			color: #FE0100;
		}
	}
</style>
