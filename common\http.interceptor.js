/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作
const install = (app, vm) => {
	// 通用请求头设定
	const ajaxHeader = 'x-ajax';
	const sessionIdHeader = 'Authorization';
	const rememberMeHeader = 'x-remember';

	// Vue 3 中通过 app.config.globalProperties 访问全局属性
	const $u = app.config.globalProperties.$u;
	if (!$u || !$u.http) {
		console.warn('uView Plus $u.http 不可用，跳过 HTTP 拦截器配置');
		return;
	}

	// 检查 setConfig 方法是否可用
	if (typeof $u.http.setConfig !== 'function') {
		console.warn('$u.http.setConfig 方法不可用，跳过配置');
		return;
	}

	console.log('✅ uView HTTP 模块检查通过');

	// 获取 store 实例
	const store = app.config.globalProperties.$store;
	if (!store) {
		console.warn('Vuex store 不可用，使用默认配置');
	}

	// 为了兼容性，将 $u 方法也挂载到 app.config.globalProperties
	if (!app.config.globalProperties.$u.toast) {
		// 如果 toast 方法不存在，使用 uni.showToast 作为备用
		app.config.globalProperties.$u.toast = (title) => {
			uni.showToast({ title, icon: 'none' });
		};
	}

	// 提供一个使用原生store的vuex方法
	app.config.globalProperties.$u.vuex = (key, value) => {
		try {
			if (store && store.commit) {
				store.commit('$uStore', {
					name: key,
					value: value
				});
				console.log('✅ $u.vuex 使用原生store成功:', key, value);
			} else {
				console.warn('❌ store不可用:', key, value);
			}
		} catch (error) {
			console.error('❌ $u.vuex 执行失败:', error, key, value);
		}
	};

	// 🔄 自动恢复token到Vuex（如果本地存储有但Vuex没有）
	const autoRecoverToken = () => {
		const currentToken = store?.state?.vuex_token;
		if (!currentToken) {
			// 尝试从不同的本地存储位置恢复token
			const remoteTokenData = uni.getStorageSync('RemoteTokenData');
			const userTokenData = uni.getStorageSync('userToken');

			let tokenToRecover = null;
			if (remoteTokenData?.data?.data?.token) {
				tokenToRecover = remoteTokenData.data.data.token;
				console.log('🔄 从RemoteTokenData恢复token');
			} else if (userTokenData?.Token) {
				tokenToRecover = userTokenData.Token;
				console.log('🔄 从userToken恢复token');
			}

			if (tokenToRecover && store?.commit) {
				store.commit('$uStore', { name: 'vuex_token', value: tokenToRecover });
				console.log('✅ 自动恢复token成功:', tokenToRecover);
			}
		}
	};

	// 执行自动恢复
	autoRecoverToken();

	// 修正：使用回调函数方式配置 setConfig
	$u.http.setConfig((config) => {
		config.baseURL = store ? store.state.apiurl : 'http://localhost:8980';
		config.originalData = true;
		// 默认头部，http2约定header名称统一小写
		config.header = {
			'Platform': 2,
			'content-type': 'application/x-www-form-urlencoded',
			'x-requested-with': 'XMLHttpRequest'
		};
		return config;
	});

	// 请求拦截，配置Token等参数 - 修复检查逻辑
	console.log('🔍 检查拦截器可用性:', {
		interceptor: !!$u.http.interceptor,
		interceptorType: typeof $u.http.interceptor,
		hasRequest: !!$u.http.interceptor?.request,
		hasResponse: !!$u.http.interceptor?.response
	});

	// 修复：更宽松的检查条件，支持不同版本的uView
	try {
		// 尝试直接设置拦截器
		if ($u.http.interceptor) {
			console.log('✅ 使用 $u.http.interceptor');
			setupInterceptors($u.http.interceptor);
		} else if ($u.http.interceptors) {
			console.log('✅ 使用 $u.http.interceptors');
			setupInterceptors($u.http.interceptors);
		} else {
			console.warn('⚠️ 拦截器对象不可用，尝试直接设置');
			// 尝试直接创建拦截器对象
			$u.http.interceptor = {};
			setupInterceptors($u.http.interceptor);
		}
	} catch (error) {
		console.error('❌ 拦截器设置失败:', error);
	}

	// 拦截器设置函数
	function setupInterceptors(interceptorObj) {
		// 请求拦截器
		interceptorObj.request = (req) => {
			if (!req.header){
				req.header = {};
			}
			req.header["source"] = "app";

			// 默认指定返回 JSON 数据
			if (!req.header[ajaxHeader]){
				req.header[ajaxHeader] = 'json';
			}

			// 🔑 自动添加Token - 每次请求前都检查
			const token = store ? store.state.vuex_token : '';
			if (!req.header[sessionIdHeader] && token){
				req.header[sessionIdHeader] = token;
				console.log('🔑 自动添加Token到请求头');
			} else if (!token) {
				console.log('⚠️ 请求时未找到token');
			}

			// 为节省流量，记住我数据不是每次都发送的，当会话失效后，尝试重试登录
			const remember = store ? store.state.vuex_remember : '';
			if (!req.header[rememberMeHeader] && remember && req.remember){
				req.header[rememberMeHeader] = remember;
				req.remember = false;
			}
			console.log('📤 请求拦截器处理完成:', req.url);
			return req;
		};

		// 响应拦截器
		interceptorObj.response = async (res, req) => {
			console.log('📥 响应拦截器处理:', res.statusCode, req.url);
		
		// 处理401未授权状态码
		if (res.statusCode === 401) {
			$u.toast('登录已过期，请重新登录');
			// 清除token和用户信息
			uni.removeStorageSync('token');
			uni.removeStorageSync('RemoteTokenData');
			uni.removeStorageSync('userToken');

			// 使用原生store清除token
			if (store && store.commit) {
				store.commit('$uStore', { name: 'vuex_token', value: '' });
				store.commit('$uStore', { name: 'vuex_user', value: {} });
			}

			// 跳转到登录页面
			uni.reLaunch({
				url: '/pages/sys/login/index'
			});
			return false;
		}
		if (!(res.data)){
			$u.toast('未连接到服务器')
			return Promise.reject(new Error('未连接到服务器'));
		}

		if(res.data.code !== 0) {
			$u.toast(res.data.msg)
			return Promise.reject(new Error(res.data.msg))
		}

		// 提取响应数据
		let responseData = res.data.data;
		console.log('HTTP 拦截器处理的数据:', responseData)

		// 处理用户认证相关数据
		if (typeof responseData === 'object' && !(responseData instanceof Array)){
			if (responseData.token){
				console.log('更新 token:', responseData.token)
				// 使用原生store更新token
				if (store && store.commit) {
					store.commit('$uStore', { name: 'vuex_token', value: responseData.token });
					if (responseData.user){
						store.commit('$uStore', { name: 'vuex_user', value: responseData.user });
					}
					store.commit('$uStore', { name: 'vuex_isAgent', value: responseData.isAgent });
				}
			}
			if (responseData.result === 'login'){
				// 使用原生store清除用户信息
				if (store && store.commit) {
					store.commit('$uStore', { name: 'vuex_user', value: {} });
				}

				if (req.tryagain == undefined || req.tryagain){
					req.tryagain = false; req.remember = true;
					await $u.http.request(req).then(res => {
						responseData = res;
					});
				}
				if (responseData.result === 'login'){
					if (!req.data.loginCheck){
						$u.toast(responseData.msg);
					}
					req.tryagain = true;
				}
			}
		}

		// 处理记住我功能
		if (res.header && res.header[rememberMeHeader]){
			let remember = res.header[rememberMeHeader];
			if (store && store.commit) {
				if (remember && remember != 'deleteMe'){
					store.commit('$uStore', { name: 'vuex_remember', value: remember });
				}else{
					store.commit('$uStore', { name: 'vuex_remember', value: '' });
				}
			}
		}

		// 返回处理后的数据，保持原有的数据结构以确保兼容性
		// 对于列表数据，直接返回数组；对于其他数据，返回完整的响应结构
		if (Array.isArray(responseData)) {
			return responseData;
		} else if (responseData && typeof responseData === 'object') {
			// 如果响应数据有 list 属性，说明是分页数据，返回完整结构
			if (responseData.list !== undefined) {
				return responseData;
			}
			// 其他情况返回原数据
			return responseData;
		}

		return responseData;
	}

	// 添加便捷方法
	if ($u) {
		// 封装 get text 请求
		$u.getText = (url, data = {}, header = {}) => {
			return $u.http.request({
				dataType: 'text',
				method: 'GET',
				url,
				header,
				data
			});
		};

		// 封装 post json 请求
		$u.postJson = (url, data = {}, header = {}) => {
			console.log('📝 POST JSON 请求:', url);
			header['content-type'] = 'application/json';
			return $u.http.request({
				url,
				method: 'POST',
				header,
				data
			});
		};

		console.log('✅ 便捷方法添加成功');
	}

	console.log('🎉 HTTP 拦截器初始化完成');
}

export default {
	install
}
