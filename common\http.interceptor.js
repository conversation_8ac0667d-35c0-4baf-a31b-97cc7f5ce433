/**
 * uView-Plus HTTP 拦截器配置
 * 专门针对 uView-Plus 优化的HTTP请求拦截器
 */
import store from '@/store'
import config from '@/common/config.js'

const sessionIdHeader = 'Authorization';
const ajaxHeader = 'X-Requested-With';
const rememberMeHeader = 'X-Remember-Me';

const install = (app) => {
	const $u = app.config.globalProperties.$u;

	// uView-Plus HTTP 模块检查
	if (!$u?.http?.setConfig) {
		console.error('❌ uView-Plus HTTP 模块未正确加载');
		return;
	}
	
	console.log('✅ uView-Plus HTTP 模块检查通过');

	// uView-Plus 方法增强
	if (!app.config.globalProperties.$u.toast) {
		app.config.globalProperties.$u.toast = (title) => {
			uni.showToast({ title, icon: 'none' });
		};
	}

	// 提供标准的vuex方法
	app.config.globalProperties.$u.vuex = (key, value) => {
		if (store?.commit) {
			store.commit('$uStore', { name: key, value });
			console.log('✅ $u.vuex:', key);
		} else {
			console.warn('❌ Vuex store 不可用');
		}
	};

	// 🔄 自动恢复token到Vuex
	const autoRecoverToken = () => {
		const currentToken = store?.state?.vuex_token;
		if (!currentToken) {
			const remoteTokenData = uni.getStorageSync('RemoteTokenData');
			const userTokenData = uni.getStorageSync('userToken');
			
			let tokenToRecover = null;
			if (remoteTokenData?.data?.data?.token) {
				tokenToRecover = remoteTokenData.data.data.token;
				console.log('🔄 从RemoteTokenData恢复token');
			} else if (userTokenData?.Token) {
				tokenToRecover = userTokenData.Token;
				console.log('🔄 从userToken恢复token');
			}
			
			if (tokenToRecover && store?.commit) {
				store.commit('$uStore', { name: 'vuex_token', value: tokenToRecover });
				console.log('✅ 自动恢复token成功');
			}
		}
	};
	
	autoRecoverToken();

	// uView-Plus HTTP 配置
	$u.http.setConfig((config) => {
		config.baseURL = store?.state?.apiurl || 'http://localhost:8980';
		config.originalData = true;
		config.header = {
			'Platform': 2,
			'content-type': 'application/x-www-form-urlencoded',
			'x-requested-with': 'XMLHttpRequest'
		};
		return config;
	});
	
	// uView-Plus 请求拦截器 - 使用正确的API
	$u.http.interceptors.request.use((config) => {
		// 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
		config.data = config.data || {};
		
		if (!config.header) {
			config.header = {};
		}
		config.header["source"] = "app";
		
		// 默认指定返回 JSON 数据
		if (!config.header[ajaxHeader]) {
			config.header[ajaxHeader] = 'json';
		}
		
		// 🔑 自动添加Token
		const token = store?.state?.vuex_token || '';
		if (!config.header[sessionIdHeader] && token) {
			config.header[sessionIdHeader] = token;
			console.log('🔑 添加Token到请求头');
		}

		// 记住我功能
		const remember = store?.state?.vuex_remember || '';
		if (!config.header[rememberMeHeader] && remember && config.remember) {
			config.header[rememberMeHeader] = remember;
			config.remember = false;
		}
		
		console.log('📤 请求拦截:', config.url);
		return config;
	}, (config) => {
		// 请求错误处理
		return Promise.reject(config);
	});

	// uView-Plus 响应拦截器 - 使用正确的API
	$u.http.interceptors.response.use((response) => {
		console.log('📥 响应拦截:', response.statusCode, response.config?.url);
		
		// 处理401未授权状态码
		if (response.statusCode === 401) {
			$u.toast('登录已过期，请重新登录');
			// 清除token和用户信息
			uni.removeStorageSync('token');
			uni.removeStorageSync('RemoteTokenData');
			uni.removeStorageSync('userToken');
			
			// 使用原生store清除token
			if (store && store.commit) {
				store.commit('$uStore', { name: 'vuex_token', value: '' });
				store.commit('$uStore', { name: 'vuex_user', value: {} });
			}
			
			// 跳转到登录页面
			uni.reLaunch({
				url: '/pages/sys/login/index'
			});
			return Promise.reject(response);
		}

		// 检查服务器连接
		if (!response.data) {
			$u.toast('未连接到服务器');
			return Promise.reject(new Error('未连接到服务器'));
		}

		// 获取响应数据
		const data = response.data;
		const custom = response.config?.custom;

		// 处理业务逻辑错误
		if (data.code !== 0) {
			// 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
			if (custom?.toast !== false) {
				$u.toast(data.msg || '请求失败');
			}
			// 如果需要catch返回，则进行reject
			if (custom?.catch) {
				return Promise.reject(data);
			} else {
				// 否则返回一个pending中的promise
				return new Promise(() => {});
			}
		}

		// 处理用户认证相关数据
		if (typeof data === 'object' && !(data instanceof Array)) {
			if (data.token) {
				console.log('🔄 更新token:', data.token);
				// 使用原生store更新token
				if (store && store.commit) {
					store.commit('$uStore', { name: 'vuex_token', value: data.token });
					if (data.user) {
						store.commit('$uStore', { name: 'vuex_user', value: data.user });
					}
					store.commit('$uStore', { name: 'vuex_isAgent', value: data.isAgent });
				}
			}
		}

		// 处理记住我功能
		if (response.header && response.header[rememberMeHeader]) {
			let remember = response.header[rememberMeHeader];
			if (store && store.commit) {
				if (remember && remember != 'deleteMe') {
					store.commit('$uStore', { name: 'vuex_remember', value: remember });
				} else {
					store.commit('$uStore', { name: 'vuex_remember', value: '' });
				}
			}
		}

		// 返回数据
		return data.data || data || {};
	}, (response) => {
		// 对响应错误做点什么 （statusCode !== 200）
		console.log('❌ 响应错误:', response);
		return Promise.reject(response);
	});
	
	// uView-Plus 便捷方法
	$u.getText = (url, data = {}, header = {}) => {
		return $u.http.request({
			dataType: 'text',
			method: 'GET',
			url,
			header,
			data
		});
	};

	$u.postJson = (url, data = {}, header = {}) => {
		console.log('📝 POST JSON:', url);
		header['content-type'] = 'application/json';
		return $u.http.request({
			url,
			method: 'POST',
			header,
			data
		});
	};
	
	console.log('🎉 uView-Plus HTTP 拦截器初始化完成');
}

export default {
	install
}
