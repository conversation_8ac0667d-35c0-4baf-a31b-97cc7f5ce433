/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作
const install = (app, vm) => {
	// 通用请求头设定
	const ajaxHeader = 'x-ajax';
	const sessionIdHeader = 'Authorization';
	const rememberMeHeader = 'x-remember';

	// Vue 3 中通过 app.config.globalProperties 访问全局属性
	const $u = app.config.globalProperties.$u;
	if (!$u || !$u.http) {
		console.warn('uView Plus $u.http 不可用，跳过 HTTP 拦截器配置');
		return;
	}

	// uView-Plus HTTP 模块检查
	if (!$u?.http?.setConfig) {
		console.error('❌ uView-Plus HTTP 模块未正确加载');
		return;
	}

	console.log('✅ uView-Plus HTTP 模块检查通过');

	// 获取 store 实例
	const store = app.config.globalProperties.$store;
	if (!store) {
		console.warn('Vuex store 不可用，使用默认配置');
	}

	// uView-Plus 方法增强
	if (!app.config.globalProperties.$u.toast) {
		app.config.globalProperties.$u.toast = (title) => {
			uni.showToast({ title, icon: 'none' });
		};
	}

	// 提供标准的vuex方法
	app.config.globalProperties.$u.vuex = (key, value) => {
		if (store?.commit) {
			store.commit('$uStore', { name: key, value });
			console.log('✅ $u.vuex:', key);
		} else {
			console.warn('❌ Vuex store 不可用');
		}
	};

	// 🔄 自动恢复token到Vuex
	const autoRecoverToken = () => {
		const currentToken = store?.state?.vuex_token;
		if (!currentToken) {
			const remoteTokenData = uni.getStorageSync('RemoteTokenData');
			const userTokenData = uni.getStorageSync('userToken');

			let tokenToRecover = null;
			if (remoteTokenData?.data?.data?.token) {
				tokenToRecover = remoteTokenData.data.data.token;
				console.log('🔄 从RemoteTokenData恢复token');
			} else if (userTokenData?.Token) {
				tokenToRecover = userTokenData.Token;
				console.log('🔄 从userToken恢复token');
			}

			if (tokenToRecover && store?.commit) {
				store.commit('$uStore', { name: 'vuex_token', value: tokenToRecover });
				console.log('✅ 自动恢复token成功');
			}
		}
	};

	autoRecoverToken();

	// uView-Plus HTTP 配置
	$u.http.setConfig((config) => {
		config.baseURL = store?.state?.apiurl || 'http://localhost:8980';
		config.originalData = true;
		config.header = {
			'Platform': 2,
			'content-type': 'application/x-www-form-urlencoded',
			'x-requested-with': 'XMLHttpRequest'
		};
		return config;
	});

	// uView-Plus 请求拦截器 - 使用正确的API
	$u.http.interceptors.request.use((config) => {
		// 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
		config.data = config.data || {};

		if (!config.header) {
			config.header = {};
		}
		config.header["source"] = "app";

		// 默认指定返回 JSON 数据
		if (!config.header[ajaxHeader]) {
			config.header[ajaxHeader] = 'json';
		}

		// 🔑 自动添加Token
		const token = store?.state?.vuex_token || '';
		if (!config.header[sessionIdHeader] && token) {
			config.header[sessionIdHeader] = token;
			console.log('🔑 添加Token到请求头');
		}

		// 记住我功能
		const remember = store?.state?.vuex_remember || '';
		if (!config.header[rememberMeHeader] && remember && config.remember) {
			config.header[rememberMeHeader] = remember;
			config.remember = false;
		}

		console.log('📤 请求拦截:', config.url);
		return config;
	}, (config) => {
		// 请求错误处理
		return Promise.reject(config);
	});

	// uView-Plus 响应拦截器 - 使用正确的API
	$u.http.interceptors.response.use((response) => {
		console.log('📥 响应拦截:', response.statusCode, response.config?.url);

		// 处理401未授权状态码
		if (response.statusCode === 401) {
			$u.toast('登录已过期，请重新登录');
			// 清除token和用户信息
			uni.removeStorageSync('token');
			uni.removeStorageSync('RemoteTokenData');
			uni.removeStorageSync('userToken');

			// 使用原生store清除token
			if (store && store.commit) {
				store.commit('$uStore', { name: 'vuex_token', value: '' });
				store.commit('$uStore', { name: 'vuex_user', value: {} });
			}

			// 跳转到登录页面
			uni.reLaunch({
				url: '/pages/sys/login/index'
			});
			return Promise.reject(response);
		}
		if (!(res.data)){
			$u.toast('未连接到服务器')
			return Promise.reject(new Error('未连接到服务器'));
		}

		if(res.data.code !== 0) {
			$u.toast(res.data.msg)
			return Promise.reject(new Error(res.data.msg))
		}

		// 提取响应数据
		let responseData = res.data.data;
		console.log('HTTP 拦截器处理的数据:', responseData)

		// 处理用户认证相关数据
		if (typeof responseData === 'object' && !(responseData instanceof Array)){
			if (responseData.token){
				console.log('更新 token:', responseData.token)
				// 使用原生store更新token
				if (store && store.commit) {
					store.commit('$uStore', { name: 'vuex_token', value: responseData.token });
					if (responseData.user){
						store.commit('$uStore', { name: 'vuex_user', value: responseData.user });
					}
					store.commit('$uStore', { name: 'vuex_isAgent', value: responseData.isAgent });
				}
			}
			if (responseData.result === 'login'){
				// 使用原生store清除用户信息
				if (store && store.commit) {
					store.commit('$uStore', { name: 'vuex_user', value: {} });
				}

				if (req.tryagain == undefined || req.tryagain){
					req.tryagain = false; req.remember = true;
					await $u.http.request(req).then(res => {
						responseData = res;
					});
				}
				if (responseData.result === 'login'){
					if (!req.data.loginCheck){
						$u.toast(responseData.msg);
					}
					req.tryagain = true;
				}
			}
		}

		// 处理记住我功能
		if (res.header && res.header[rememberMeHeader]){
			let remember = res.header[rememberMeHeader];
			if (store && store.commit) {
				if (remember && remember != 'deleteMe'){
					store.commit('$uStore', { name: 'vuex_remember', value: remember });
				}else{
					store.commit('$uStore', { name: 'vuex_remember', value: '' });
				}
			}
		}

		// 返回处理后的数据，保持原有的数据结构以确保兼容性
		// 对于列表数据，直接返回数组；对于其他数据，返回完整的响应结构
		if (Array.isArray(responseData)) {
			return responseData;
		} else if (responseData && typeof responseData === 'object') {
			// 如果响应数据有 list 属性，说明是分页数据，返回完整结构
			if (responseData.list !== undefined) {
				return responseData;
			}
			// 其他情况返回原数据
			return responseData;
		}

		return responseData;
	};

	// uView-Plus 便捷方法
	$u.getText = (url, data = {}, header = {}) => {
		return $u.http.request({
			dataType: 'text',
			method: 'GET',
			url,
			header,
			data
		});
	};

	$u.postJson = (url, data = {}, header = {}) => {
		console.log('📝 POST JSON:', url);
		header['content-type'] = 'application/json';
		return $u.http.request({
			url,
			method: 'POST',
			header,
			data
		});
	};

	console.log('🎉 uView-Plus HTTP 拦截器初始化完成');
}

export default {
	install
}
