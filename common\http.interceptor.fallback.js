/**
 * 备用 HTTP 拦截器 - 当 interceptor 对象不可用时使用
 */

const install = (app, vm) => {
	console.log('🔧 初始化备用 HTTP 拦截器...');
	
	// 检查 uView Plus 是否可用
	const $u = app.config.globalProperties.$u;
	if (!$u) {
		console.warn('uView Plus 不可用，跳过 HTTP 拦截器配置');
		return;
	}
	
	console.log('📦 uView Plus 可用:', !!$u);
	console.log('🌐 HTTP 模块可用:', !!$u.http);
	console.log('🔗 interceptor 对象:', typeof $u.http?.interceptor);
	
	if (!$u.http) {
		console.warn('$u.http 模块不可用，跳过 HTTP 拦截器配置');
		return;
	}
	
	// 获取 store 实例
	const store = app.config.globalProperties.$store;
	console.log('🗄️ Store 可用:', !!store);
	console.log('📡 API URL:', store?.state?.apiurl || '未配置');
	
	try {
		// 由于 interceptor 不可用，我们包装原始的 request 方法
		console.log('⚠️ interceptor 不可用，包装原始 request 方法...');
		
		if ($u.http.request && typeof $u.http.request === 'function') {
			// 保存原始的 request 方法
			const originalRequest = $u.http.request.bind($u.http);
			
			// 包装 request 方法
			$u.http.request = async (options = {}) => {
				console.log('📤 包装请求:', options.url);
				
				// 请求预处理
				try {
					// 设置基础 URL（如果没有完整 URL）
					if (options.url && !options.url.startsWith('http')) {
						const baseUrl = store?.state?.apiurl || 'http://localhost:8980';
						options.url = baseUrl + (options.url.startsWith('/') ? '' : '/') + options.url;
						console.log('🌐 完整 URL:', options.url);
					}
					
					// 设置默认头部
					if (!options.header) {
						options.header = {};
					}
					
					// 添加平台标识
					if (!options.header['Platform']) {
						options.header['Platform'] = 2;
					}
					
					// 添加请求头
					if (!options.header['x-requested-with']) {
						options.header['x-requested-with'] = 'XMLHttpRequest';
					}
					
					// 添加 Token
					const token = store?.state?.vuex_token || '';
					if (token && !options.header['Authorization']) {
						options.header['Authorization'] = token;
						console.log('🔑 添加 Token');
					}
					
					// 添加记住我
					const remember = store?.state?.vuex_remember || '';
					if (remember && options.remember && !options.header['x-remember']) {
						options.header['x-remember'] = remember;
						options.remember = false;
						console.log('💾 添加记住我');
					}
					
					console.log('📤 最终请求配置:', {
						url: options.url,
						method: options.method,
						header: options.header
					});
					
				} catch (error) {
					console.error('❌ 请求预处理失败:', error);
				}
				
				// 调用原始请求方法
				try {
					const response = await originalRequest(options);
					console.log('📥 收到响应:', response.statusCode);
					
					// 响应后处理
					try {
						// 处理 401 未授权
						if (response.statusCode === 401) {
							console.log('🚫 登录已过期');
							
							// 显示提示
							if ($u.toast) {
								$u.toast('登录已过期，请重新登录');
							} else {
								uni.showToast({ title: '登录已过期，请重新登录', icon: 'none' });
							}
							
							// 清除 token
							uni.removeStorageSync('token');
							uni.removeStorageSync('RemoteTokenData');
							uni.removeStorageSync('userToken');

							if (store && store.commit) {
								store.commit('$uStore', { name: 'vuex_token', value: '' });
								store.commit('$uStore', { name: 'vuex_user', value: {} });
							}
							
							// 跳转到登录页
							setTimeout(() => {
								uni.reLaunch({
									url: '/pages/sys/login/index'
								});
							}, 1500);
							
							return Promise.reject(new Error('登录已过期'));
						}
						
						// 处理服务器错误
						if (!response.data) {
							console.log('❌ 服务器无响应');
							if ($u.toast) {
								$u.toast('未连接到服务器');
							} else {
								uni.showToast({ title: '未连接到服务器', icon: 'none' });
							}
							return Promise.reject(new Error('未连接到服务器'));
						}
						
						// 处理业务错误
						if (response.data.code !== undefined && response.data.code !== 0) {
							console.log('❌ 业务错误:', response.data.msg);
							if ($u.toast) {
								$u.toast(response.data.msg || '请求失败');
							} else {
								uni.showToast({ title: response.data.msg || '请求失败', icon: 'none' });
							}
							return Promise.reject(new Error(response.data.msg || '请求失败'));
						}
						
						// 处理 token 更新
						if (response.data && typeof response.data === 'object' && response.data.token) {
							console.log('🔄 更新 token:', response.data.token);
							if (store && store.commit) {
								store.commit('$uStore', { name: 'vuex_token', value: response.data.token });
								if (response.data.user) {
									store.commit('$uStore', { name: 'vuex_user', value: response.data.user });
								}
							}
						}
						
						// 处理记住我
						if (response.header && response.header['x-remember']) {
							const remember = response.header['x-remember'];
							if (store && store.commit) {
								if (remember && remember !== 'deleteMe') {
									store.commit('$uStore', { name: 'vuex_remember', value: remember });
								} else {
									store.commit('$uStore', { name: 'vuex_remember', value: '' });
								}
							}
						}
						
						console.log('✅ 请求成功');
						return response.data?.data || response.data || response;
						
					} catch (error) {
						console.error('❌ 响应后处理失败:', error);
						return response;
					}
					
				} catch (error) {
					console.error('❌ 请求失败:', error);
					throw error;
				}
			};
			
			console.log('✅ request 方法包装成功');
		} else {
			console.warn('⚠️ request 方法不可用');
		}
		
		// 添加便捷方法
		if ($u) {
			// GET 文本请求
			$u.getText = (url, data = {}, header = {}) => {
				console.log('📄 GET 文本请求:', url);
				return $u.http.request({
					dataType: 'text',
					method: 'GET',
					url,
					header,
					data
				});
			};
			
			// POST JSON 请求
			$u.postJson = (url, data = {}, header = {}) => {
				console.log('📝 POST JSON 请求:', url);
				header['content-type'] = 'application/json';
				return $u.http.request({
					url,
					method: 'POST',
					header,
					data
				});
			};
			
			console.log('✅ 便捷方法添加成功');
		}
		
	} catch (error) {
		console.error('❌ HTTP 拦截器配置失败:', error);
		console.error('错误详情:', error.message);
		
		// 即使失败也不阻止应用启动
		console.log('⚠️ 继续启动应用，但 HTTP 拦截器功能可能不完整');
	}
	
	console.log('🎉 备用 HTTP 拦截器初始化完成');
}

export default {
	install
}
