<template>
	<view class="wrap">
		<up-cell-group :border="false" title="消息提醒">
			<up-cell title="接受消息提醒" :arrow="false">
				<template #value>
					<up-switch v-model="message" slot="right-icon" class="up-m-l-20"></up-switch>
				</template>
			</up-cell>
			<up-cell title="配布单通知" :arrow="false" label="关闭后，将停止检查新配布单并停止相关通知。">
				<template #value>
					<up-switch v-model="notificationEnabled" @change="onNotificationChange" slot="right-icon" class="up-m-l-20"></up-switch>
				</template>
			</up-cell>
			<up-cell title="通知栏显示消息详情" :arrow="false" label="关闭后，当收到消息的时候，只显示有提示，不显示消息内容。">
				<template #value>
					<up-switch v-model="messageBar" slot="right-icon" class="up-m-l-20"></up-switch>
				</template>
			</up-cell>
		</up-cell-group>
		<up-cell-group :border="false" title="声音与振动" label="前往系统设置中，修改声音与振动">
			<up-cell title="收到消息后播放声音或振动" @click="openSettings">
			</up-cell>
		</up-cell-group>
		<up-cell-group :border="false" title="软件更新提醒">
			<up-cell title="软件更新提醒" :arrow="false" label="当本软件有新版本发布时，给予提醒">
				<template #value>
					<up-switch v-model="upgrade" slot="right-icon" class="up-m-l-20"></up-switch>
				</template>
			</up-cell>
		</up-cell-group>
	</view>
</template>
<script>
/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
export default {
	data() {
		return {
			message: true,
			messageBar: true,
			upgrade: true
		};
	},
	computed: {
		notificationEnabled: {
			get() {
				return this.$store.state.vuex_notificationEnabled;
			},
			set(value) {
				this.$store.commit('$uStore', {
					name: 'vuex_notificationEnabled',
					value: value
				});
			}
		}
	},
	methods: {
		onNotificationChange(value) {
			if (value) {
				// 开启通知，重新初始化通知系统
				this.$store.dispatch('initNotification');
			} else {
				// 关闭通知，停止轮询
				this.$store.dispatch('stopPolling');
			}
		},
		openSettings() {
			// #ifdef APP-PLUS
			uni.getSystemInfo({  
				success(res) {  
					if(res.platform == 'ios'){
						plus.runtime.openURL("app-settings://");
					} else if (res.platform == 'android'){
						var main = plus.android.runtimeMainActivity();  
						var Intent = plus.android.importClass("android.content.Intent");
						var mIntent = new Intent('android.settings.SOUND_SETTINGS');
						main.startActivity(mIntent);
					}
				}
			});
			// #endif
			// #ifndef APP-PLUS
			this.$u.toast('小程序端或H5端已是最新版，无需检查更新！');
			// #endif
		},
	}
};
</script>
<style lang="scss">


page {
	background-color: #f8f8f8;
}
::v-deep( .up-cell-title) {
	padding: 25rpx 30rpx;
	font-size: 30rpx;
}
::v-deep(.u-cell){
	background: white;
}
</style>
