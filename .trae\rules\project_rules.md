# PDA项目开发规范

## 1. 项目技术栈
- **前端框架**: uni-app (Vue 3)
- **构建工具**: Vite
- **UI框架**: uView Plus
- **状态管理**: Vuex
- **HTTP请求**: uni.request 封装 (通过 `common/http.api.js` 和 `common/http.interceptor.js`)
- **国际化**: vue-i18n
- **样式预处理**: Sass/SCSS
- **工具库**: dayjs, lodash-es, currency.js

## 2. 项目结构规范
```
pda/
├── common/                 # 公共JS文件、配置、HTTP API定义、拦截器、国际化文件等
│   ├── config.js           # 环境配置
│   ├── http.api.js         # API接口定义
│   ├── http.interceptor.js # HTTP拦截器
│   ├── locales/            # 国际化文件
│   └── ...
├── components/             # 可复用的Vue组件，按功能或业务模块划分
│   ├── card/               # 业务卡片组件
│   ├── common-navbar/      # 通用导航栏
│   ├── searchView/         # 搜索视图
│   └── ...
├── pages/                  # 页面文件，按业务模块划分
│   ├── basedata/           # 基础数据模块
│   ├── sys/                # 系统模块
│   └── ...
├── static/                 # 静态资源，如图片、字体等
├── store/                  # Vuex状态管理模块
├── utils/                  # 工具函数
└── dev/services/           # 后端API服务定义
```

## 3. 代码规范

### 3.1 API 调用规范
- **统一管理**: 所有后端API接口定义必须集中在 `common/http.api.js` 中
- **调用方式**: 页面或组件中调用API必须通过 `this.$u.api.xxx` 的形式，禁止直接使用 `uni.request`
- **参数传递**: 明确定义API接口的请求参数，并进行必要的参数校验
- **响应处理**: API响应统一通过Promise的 `.then()` 和 `.catch()` 进行处理，避免使用回调函数
- **错误处理**: 统一在 `http.interceptor.js` 中处理全局的HTTP请求错误（如网络错误、状态码错误等），并在业务层面进行细化处理

### 3.2 状态管理规范 (Vuex)
- **模块化**: 根据业务模块划分Vuex模块，每个模块有独立的 `state`, `mutations`, `actions`, `getters`
- **命名规范**: `mutations` 使用大写常量命名，`actions` 使用驼峰命名
- **数据流**: 严格遵循Vuex单向数据流，组件通过 `dispatch` 触发 `actions`，`actions` 提交 `mutations` 修改 `state`

### 3.3 组件开发规范
- **复用性**: 优先考虑开发可复用组件，减少重复代码
- **命名**: 组件文件名使用大驼峰命名法（PascalCase），如 `MyComponent.vue`
- **Props**: 明确定义组件的 `props`，包括类型、是否必填、默认值等
- **Events**: 使用 `emit` 触发自定义事件，事件名使用 kebab-case（短横线命名法）
- **插槽**: 灵活使用 `slot` 实现组件内容的定制化
- **样式**: 组件样式应使用 `scoped` 限制作用域，避免全局污染

### 3.4 HTTP 请求规范
- **拦截器**: 在 `common/http.interceptor.js` 中统一处理请求头（如token）、请求参数、响应数据、错误处理、loading状态等
- **Loading 提示**: 对于耗时操作，应在请求开始时显示loading，请求结束时隐藏loading，并处理好loading的显示与隐藏逻辑，避免出现loading一直显示的问题

### 3.5 错误处理规范
- **全局错误捕获**: 使用Vue的 `errorHandler` 或 `uni.onError` 捕获全局错误
- **业务错误提示**: 对于后端返回的业务错误，统一通过 `uni.showToast` 或 `uni.showModal` 进行提示
- **日志记录**: 生产环境下，将错误信息上报至日志系统

## 4. 环境配置规范
- **多环境配置**: 使用 `common/config.js` 管理不同环境（开发、测试、生产）的配置，如API地址、图片上传地址等
- **动态切换**: 确保应用可以根据打包环境自动加载对应的配置

## 5. 国际化规范
- **统一管理**: 国际化文本统一在 `common/locales` 目录下管理
- **使用方式**: 页面或组件中通过 `$t('key')` 的形式使用国际化文本
- **支持语言**: 目前支持中文(zh_CN)和英文(en)

## 6. 性能优化规范
- **图片优化**: 压缩图片，使用适当的图片格式和尺寸
- **组件懒加载**: 对于不常用的组件或页面，考虑使用懒加载
- **数据缓存**: 对于不经常变动的数据，考虑使用本地缓存
- **列表渲染优化**: 对于长列表，使用虚拟列表等技术进行优化

## 7. 测试规范
- **单元测试**: 对核心功能和工具函数编写单元测试
- **集成测试**: 对关键业务流程进行集成测试

## 8. 部署规范
- **打包**: 统一使用构建命令进行打包
- **版本管理**: 遵循语义化版本规范

## 9. Git 提交规范
- **Commit Message**: 遵循 Conventional Commits 规范，如 `feat: add new feature`，`fix: fix bug`

## 10. 安全规范
- **数据加密**: 敏感数据传输和存储应进行加密
- **XSS/CSRF 防护**: 采取措施防止常见的Web安全漏洞

## 11. 代码审查规范
- **定期审查**: 定期进行代码审查，确保代码质量和规范性
- **关注点**: 关注代码的可读性、可维护性、性能、安全性、错误处理等

## 12. 文档规范
- **README.md**: 项目根目录应包含详细的 `README.md`，说明项目介绍、安装、运行、打包等
- **组件文档**: 复杂组件应提供使用说明和示例
- **API 文档**: 保持API文档与代码同步更新

## 常用组件库 (components/)

以下是项目中常用的组件，在新建页面时，请优先考虑复用这些组件：

### 业务卡片组件 (components/card/)
- **`baojiadan.vue`**: 报价单卡片组件
- **`fapiao.vue`**: 发票卡片组件
- **`hetong.vue`**: 合同卡片组件
- **`kehu.vue`**: 客户卡片组件
- **`form.vue`**: 表单卡片组件，用于展示表单信息
- **`genjin.vue`**: 跟进记录卡片组件
- **`qiandao.vue`**: 签到卡片组件
- **`shangji.vue`**: 商机卡片组件
- **`threadCard.vue`**: 线程卡片组件
- **`productweaveview.vue`**: 产品织造视图卡片
- **`lianXiRen.vue`**: 联系人卡片组件
- **各种业务单据卡片**: 如 `storeGoodsBusinessInItem.vue`、`storeYarnBusinessOutItem.vue` 等，用于展示不同类型的业务单据

### 导航组件
- **`common-navbar/index.vue`**: 通用导航栏组件，用于页面顶部导航
- **`headnavbar/index.vue`**: 头部导航栏组件，可能与 `common-navbar` 功能类似或互补

### 表单组件
- **`expandable-form/expandable-form.vue`**: 可展开/收起的表单组件，用于复杂表单的布局和交互
  - 支持基础字段和展开字段的分离显示
  - 提供展开/收起动画效果
  - 可自定义标题和按钮文本
- **`mx-datepicker/mx-datepicker.vue`**: 日期选择器组件，用于日期和时间的选择
- **`qianziyu-select/qianziyu-select.vue`**: 自定义选择器组件，用于特定业务的选择需求

### 数据展示组件
- **`wyb-table/wyb-table.vue`**: 功能强大的表格组件
  - 支持多选、排序、固定列等功能
  - 可自定义列宽、样式、加载状态
  - 支持底部统计行
- **`dataNull/dataNull.vue`**: 数据为空时的占位组件，用于友好提示用户
- **`skeleton/skeleton.vue`**: 骨架屏组件，用于数据加载时的占位显示，提升用户体验

### 交互组件
- **`searchView/searchView.vue`**: 搜索视图组件
  - 包含搜索框和搜索按钮
  - 支持扫码搜索功能
  - 可自定义占位符和按钮文本
- **`topDropdown/topDropdown.vue`**: 顶部下拉菜单组件，用于筛选或排序等功能
- **`getMore/getMore.vue`**: 加载更多组件，通常用于列表底部，提示加载状态
- **`addBtn/addBtn.vue`**: 添加按钮组件，用于触发添加操作

### 上传组件
- **`upload/uploadImg.vue`**: 图片上传组件
  - 支持图片选择和上传功能
  - 显示上传进度
  - 集成uniCloud文件上传

### 其他组件
- **`lianxiRow/lianxiRow.vue`**: 联系人行组件，用于展示联系人信息

## 组件使用建议

### 优先级原则
1. **优先复用**: 在新建页面或功能模块前，请先查阅 `components/` 目录，看是否有可复用的组件
2. **适配改造**: 如果现有组件不能完全满足需求，考虑是否可以通过props、slots等方式进行适配
3. **扩展开发**: 如果需要新功能，考虑是否可以基于现有组件进行扩展
4. **新建组件**: 只有在确实无法复用现有组件时，才创建新组件

### 组件选择指南
- **数据展示**: 优先使用 `card/` 目录下的业务卡片组件
- **表单输入**: 使用 `expandable-form` 处理复杂表单，`mx-datepicker` 处理日期选择
- **列表展示**: 使用 `wyb-table` 展示表格数据
- **搜索功能**: 使用 `searchView` 组件
- **空状态**: 使用 `dataNull` 组件
- **加载状态**: 使用 `skeleton` 组件
- **文件上传**: 使用 `upload/uploadImg` 组件

### 新组件开发规范
- 新创建的通用组件应放置在 `components/` 目录下
- 按功能分类创建子目录
- 遵循上述组件开发规范
- 提供清晰的props定义和使用示例
- 考虑组件的可扩展性和复用性

## 页面开发模式

### 典型页面结构
```vue
<template>
  <view class="page-container">
    <!-- 导航栏 -->
    <common-navbar :title="pageTitle" />
    
    <!-- 搜索区域 -->
    <searchView 
      v-if="showSearch"
      :placeholder-str="searchPlaceholder"
      @searchViewClickFun="handleSearch"
    />
    
    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 数据列表 -->
      <view v-if="dataList.length > 0">
        <card-component 
          v-for="item in dataList" 
          :key="item.id"
          :item="item"
          @click="handleItemClick"
        />
      </view>
      
      <!-- 空数据状态 -->
      <dataNull v-else-if="!loading" />
      
      <!-- 骨架屏 -->
      <skeleton v-if="loading" />
    </view>
    
    <!-- 加载更多 -->
    <getMore v-if="hasMore" @loadMore="loadMoreData" />
  </view>
</template>

<script>
export default {
  data() {
    return {
      pageTitle: '页面标题',
      showSearch: true,
      searchPlaceholder: '请输入搜索内容',
      dataList: [],
      loading: false,
      hasMore: true
    }
  },
  onLoad() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const res = await this.$u.api.getData()
        this.dataList = res.data
      } catch (error) {
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleSearch(keyword) {
      // 搜索逻辑
    },
    handleItemClick(item) {
      // 点击处理逻辑
    },
    loadMoreData() {
      // 加载更多逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-area {
  padding: 20rpx;
}
</style>
```

### 表单页面模式
```vue
<template>
  <view class="form-page">
    <common-navbar :title="formTitle" />
    
    <expandable-form 
      :title="'基础信息'"
      :show-expand-button="true"
    >
      <template #basic-fields>
        <!-- 基础表单字段 -->
      </template>
      
      <template #expanded-fields>
        <!-- 展开的表单字段 -->
      </template>
    </expandable-form>
    
    <!-- 提交按钮 -->
    <view class="submit-area">
      <u-button type="primary" @click="handleSubmit">提交</u-button>
    </view>
  </view>
</template>
```

这份规范文档将帮助开发团队保持代码的一致性和可维护性，同时最大化地复用现有组件，提高开发效率。