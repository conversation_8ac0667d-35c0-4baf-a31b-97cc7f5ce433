<template>
	<view class="page-container">
		<!-- 条码扫描区域 -->
		<view class="scan-section">
			<view class="scan-header">
				<view class="scan-icon">
					<text class="barcode-icon">|||</text>
				</view>
				<text class="scan-text">扫码或手动输入条码</text>
				<view class="scan-options">
					<view class="radio-group">
						<view class="radio-item" @click="toggleScanMode('add')">
							<view class="radio-circle" :class="{ active: scanMode === 'add' }">
								<view class="radio-dot" v-if="scanMode === 'add'"></view>
							</view>
							<text class="radio-label">新增</text>
						</view>
						<view class="radio-item" @click="toggleScanMode('delete')">
							<view class="radio-circle" :class="{ active: scanMode === 'delete' }">
								<view class="radio-dot" v-if="scanMode === 'delete'"></view>
							</view>
							<text class="radio-label">删除</text>
						</view>
					</view>
				</view>
			</view>
			<view class="scan-input-container">
				<input
					class="scan-input"
					type="text"
					v-model="qrBarCode"
					:focus="testFocus1"
					@confirm="goodsOutBillDetailScan"
					placeholder="请输入或扫描条码"
				/>
				<view class="confirm-btn" @click="goodsOutBillDetailScan">确认</view>
			</view>
		</view>

		<!-- 基础信息区域 -->
		<expandable-form title="基础信息">
			<template #basic-fields>
				<!-- 基本字段 -->
				<view class="form-item" @click="pickerSelectFun('单据类型')">
					<text class="form-label">类型</text>
					<view class="form-value">
						<text :class="billTypeName ? 'value-text' : 'placeholder-text'">
							{{billTypeName || '通过字典来配置'}}
						</text>
						<text class="arrow-icon">></text>
					</view>
				</view>

				<view class="form-item" @click="pickerSelectFun('营销部门')">
					<text class="form-label">营销体系</text>
					<view class="form-value">
						<text :class="planDepartmentName ? 'value-text' : 'placeholder-text'">
							{{planDepartmentName || '选择该用户能看到的体系'}}
						</text>
						<text class="arrow-icon">></text>
					</view>
				</view>

				<view class="form-item" @click="pickerSelectFun('仓库名称')">
					<text class="form-label">调出单位</text>
					<view class="form-value">
						<text :class="storeName ? 'value-text' : 'placeholder-text'">
							{{storeName || '调出单位'}}
						</text>
						<text class="arrow-icon">></text>
					</view>
				</view>

				<view class="form-item" @click="pickerSelectFun('调至仓库')">
					<text class="form-label">调入单位</text>
					<view class="form-value">
						<text :class="toStoreName ? 'value-text' : 'placeholder-text'">
							{{toStoreName || '接收单位'}}
						</text>
						<text class="arrow-icon">></text>
					</view>
				</view>
			</template>

			<template #expanded-fields>
				<!-- 展开的字段 -->
				<view class="form-item" @click="showDatePicker">
					<text class="form-label">日期</text>
					<view class="form-value">
						<text class="value-text">默认当日</text>
						<text class="arrow-icon">></text>
					</view>
				</view>

				<view class="form-item form-item-textarea">
					<text class="form-label">单据备注</text>
					<textarea
						class="form-textarea"
						v-model="remark"
						placeholder="请输入"
						maxlength="200"
					></textarea>
				</view>

				<view class="form-item form-item-textarea">
					<text class="form-label">客厂用坯单号</text>
					<input
						class="form-input"
						v-model="customerOrderNo"
						placeholder="请输入"
					/>
				</view>
			</template>
		</expandable-form>

		<!-- 出仓细码区域 -->
		<view class="detail-section">
			<view class="detail-header">
				<div class="detail-title">出仓细码</div>
				<div class="detail-summary-container">
					<text class="detail-summary">扫码添加还布</text>
					<text class="detail-count">{{fabricCount}}种还布，{{totalRolls}}匹，{{totalWeight}}kg</text>
				</div>
			</view>

			<view class="detail-list">
				<view v-for="(item, index) in outDetailList" :key="index" class="detail-item">
					<view class="detail-item-header">
						<text class="detail-code">{{item.FabricName}}#{{item.FabricColorNo}}</text>
						<text class="detail-arrow">></text>
					</view>
					<view class="detail-item-content">
						<view class="detail-row">
							<text class="detail-label">纱批：{{item.FabricCrockNo || 'xxxx'}}</text>
							<text class="detail-label">机台：15#</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">等级：二等奖</text>
							<text class="detail-label">匹数：{{item.Roll || 3}}</text>
						</view>
					</view>
					<view class="detail-tag">
						<text class="tag-text">细码 ({{item.Qty || 0}}kg)</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="bottom-actions">
			<view class="action-btn save-btn" @click="submitBtnFun">
				<text class="btn-text">保存</text>
			</view>
			<view class="action-btn submit-btn" @click="commitBtnFun">
				<text class="btn-text">提交并审核</text>
			</view>
		</view>

		<!-- 选择器组件 -->
		<up-select v-model="selectShow" :list="selectList" @confirm="selectConfirmFun"></up-select>

		<!-- 消息提示 -->
		<up-toast ref="uToast"></up-toast>

		<!-- 消息显示 -->
		<view v-if="billDataMessage" class="message-display">
			<text class="message-text">{{billDataMessage}}</text>
		</view>
	</view>
</template>

<script setup>
	import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
	import { onLoad, onBackPress } from '@dcloudio/uni-app'
	import util from '../../common/util'
	import wybTable from '@/components/wyb-table/wyb-table.vue'
	import expandableForm from '@/components/expandable-form/expandable-form.vue'

	// 响应式数据
	// 新增UI相关数据
	const scanMode = ref('add') // 扫描模式：add 或 delete
	const customerOrderNo = ref('') // 客厂用坯单号

	// 原有数据
	const selectShow = ref(false)
	const selectList = ref([])
	const selectType = ref('')
	const pageType = ref('')
	const commitType = ref('')
	const actionSheetShow = ref(false)
	const testFocus0 = ref(true)
	const testFocus1 = ref(false)
	const qrBarCode = ref('')
	const allCrockNoScanStatus = ref(false)
	const barCodeDelStatus = ref(false)
	const outBillID = ref(0)
	const outBillNo = ref('')
	const outBillDate = ref('')
	const billBusinessID = ref('')
	const billTypeName = ref('')
	const storeNameID = ref('')
	const storeName = ref('')
	const toStoreNameID = ref('')
	const toStoreName = ref('')
	const storeStationNo = ref('')
	const storeStationName = ref('')
	const customerID = ref('')
	const customerName = ref('')
	const planDepartmentID = ref('')
	const planDepartmentName = ref('')
	const saleUserID = ref('')
	const saleUserName = ref('')
	const remark = ref('')
	const fabricName = ref('')
	const fabricColorNo = ref('')
	const fabricColorName = ref('')
	const fabricCrockNo = ref('')
	const fabricSumRoll = ref(0)
	const fabricSumQty = ref(0)
	const fabricCrockNoSumRoll = ref(0)
	const fabricCrockNoSumQty = ref(0)
	const outDetailList = ref([])
	const billBusinessDataList = ref([])
	const storeNameDataList = ref([])
	const toStoreNameDataList = ref([])
	const planDepartmentDataList = ref([])
	const saleUserDataList = ref([])
	const billDataMessage = ref('')
	const commitProcName = ref('') // 审核过程名称
	const bjdDate = ref('')
	const bjdDateTime = ref(0)

	// 添加缺少的变量
	const getToStoreNameData = () => {
		// 获取调至仓库数据的方法
		console.log('获取调至仓库数据');
	}

	// 添加其他缺少的方法
	const chanpinBindFun = () => {
		console.log('产品绑定方法');
	}

	const bjdLxrBindFun = () => {
		console.log('联系人绑定方法');
	}

	const shangjiBindFun = () => {
		console.log('上级绑定方法');
	}

	const commitBtnFun = () => {
		console.log('审核按钮方法');
	}
	const headersMaster = ref([
		{
			label: '坯布名称',
			key: 'FabricName'
		},
		{
			label: '色号',
			key: 'FabricColorNo'
		},
		{
			label: '颜色',
			key: 'FabricColorName'
		},
		{
			label: '坯布缸号',
			key: 'FabricCrockNo'
		},
		{
			label: '件数',
			key: 'Roll'
		},
		{
			label: '长度',
			key: 'Qty'
		}
	])

	// 计算属性
	// 计算属性：面料种类数量
	const fabricCount = computed(() => {
		return outDetailList.value.length;
	})

	// 计算属性：总匹数
	const totalRolls = computed(() => {
		return outDetailList.value.reduce((sum, item) => sum + (item.Roll || 0), 0);
	})

	// 计算属性：总重量
	const totalWeight = computed(() => {
		return outDetailList.value.reduce((sum, item) => sum + (parseFloat(item.Qty) || 0), 0);
	})

	// 生命周期钩子
	onLoad((e) => {
		getBillBusinessData();

		setTimeout(() => {
			getStoreNameData();
		}, 500);
		setTimeout(() => {
			getPlanDepartmentData();
		}, 1000);

		if (getApp().globalData.StoreTypeNo.toUpperCase() == 'STOREFABRICGOODS') {
			storeNameID.value = getApp().globalData.StoreNameID;
			storeName.value = getApp().globalData.StoreName;
		};

		if (getApp().globalData.PlanDepartmentID != '') {
			planDepartmentID.value = getApp().globalData.PlanDepartmentID;
			planDepartmentName.value = getApp().globalData.PlanDepartmentName;
		}

		// 注释掉事件监听，这些方法需要单独实现
		// uni.$on('bjdKehuBindFun', bjdKehuBindFun)
		// uni.$on('chanpinBindFun', chanpinBindFun)
		// uni.$on('bjdLxrBindFun', bjdLxrBindFun)
		// uni.$on('shangjiBindFun', shangjiBindFun)
	})

	onBackPress(() => {
		// 注释掉事件注销，这些方法需要单独实现
		// uni.$off('bjdKehuBindFun', bjdKehuBindFun)
		// uni.$off('chanpinBindFun', chanpinBindFun)
		// uni.$off('bjdLxrBindFun', bjdLxrBindFun)
		// uni.$off('shangjiBindFun', shangjiBindFun)
	})

	// 方法定义
	// 新增UI交互方法
	const toggleScanMode = (mode) => {
		scanMode.value = mode;
		// 同步更新原有的删除状态
		barCodeDelStatus.value = (mode === 'delete');
	}

	const showDatePicker = () => {
		// 显示日期选择器的逻辑
		console.log('显示日期选择器');
	}

	const playSuccess = () => {
		util.playSuccessAudio();
	}

	const playError = () => {
		util.playErrorAudio();
	}

	const allCrockNoCheckChange = () => {
		allCrockNoScanStatus.value = !allCrockNoScanStatus.value;
	}

	const barCodeDelChange = () => {
		barCodeDelStatus.value = !barCodeDelStatus.value;
		// 同步更新扫描模式
		scanMode.value = barCodeDelStatus.value ? 'delete' : 'add';
	}

	// 展示相应数据选择框
	const pickerSelectFun = (str) => {
		selectList.value = [];
		if (str == '仓库名称') {
			console.log("---仓库名称->>>" + str);
			selectList.value = storeNameDataList.value;
		} else if (str == '调至仓库') {
			selectList.value = toStoreNameDataList.value;
		} else if (str == '单据类型') {
			selectList.value = billBusinessDataList.value;
		} else if (str == '营销部门') {
			console.log("--营销部门-->>>" + str);
			selectList.value = planDepartmentDataList.value;
		} else if (str == '销售员') {
			selectList.value = saleUserDataList.value;
		}
		console.log(JSON.stringify(selectList.value));
		selectShow.value = true;
		selectType.value = str;
	}

	// 选择框选中事件
	const selectConfirmFun = (e) => {
		if (selectType.value == '仓库名称') {
			storeNameID.value = e[0].value;
			storeName.value = e[0].label;
		} else if (selectType.value == '调至仓库') {
			toStoreNameID.value = e[0].value;
			toStoreName.value = e[0].label;
		} else if (selectType.value == '单据类型') {
			billBusinessID.value = e[0].value;
			billTypeName.value = e[0].label;
		} else if (selectType.value == '营销部门') {
			planDepartmentID.value = e[0].value;
			planDepartmentName.value = e[0].label;
		} else if (selectType.value == '销售员') {
			saleUserID.value = e[0].value;
			saleUserName.value = e[0].label;
		}
	}

	const getStoreNameData = () => {
		uni.request({
			url: util.apiurl + 'rest/db/opensql',
			data: {
				token: getApp().globalData.Token,
				format: 'json',
				data: {
					db_name: getApp().globalData.AppDBName,
					sql_command_id: 'APP.GetStoreNameList',
					params: [{
						name: 'TypeNo',
						value: '%StoreFabricGoods%'
					}]
				},
			},
			success: (res) => {
				if (res.data.status == 0 && res.data.count > 0) {
					console.log("--->>仓库名称<<<--" + JSON.stringify(res.data));
					var aResultData = res.data.data;
					for (var i = 0; i < aResultData.length; i++) {
						storeNameDataList.value.push({
							value: aResultData[i].StoreNameID,
							label: aResultData[i].StoreName
						});
						toStoreNameDataList.value.push({
							value: aResultData[i].StoreNameID,
							label: aResultData[i].StoreName
						})
					}
				} else if (res.data.status == 0 && res.data.count <= 0) {
					storeNameDataList.value = [];
					toStoreNameDataList.value = [];
				} else {
					storeNameDataList.value = [];
					toStoreNameDataList.value = [];
				}
			},
			fail: (error) => {
				uni.showToast({
					icon: 'none',
					title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
				});
				setTimeout(function() {
					uni.hideLoading();
				}, 5000);
			},
		})
	}

	const getPlanDepartmentData = () => {
		console.log("--->>>营销部门<<<---");
		uni.request({
			url: util.apiurl + 'rest/db/opensql',
			data: {
				token: getApp().globalData.Token,
				format: 'json',
				data: {
					db_name: getApp().globalData.AppDBName,
					sql_command_id: 'APP.GetPlanDepartmentOwnerList',
					params: [{
						name: 'LoginID',
						value: getApp().globalData.LoginID
					}, {
						name: 'DefaultLoginID',
						value: getApp().globalData.LoginID
					}]
				},
			},
			success: (res) => {
				if (res.data.status == 0 && res.data.count > 0) {
					console.log("---->" + JSON.stringify(res.data));
					var aResultData = res.data.data;
					for (var i = 0; i < aResultData.length; i++) {
						planDepartmentDataList.value.push({
							value: aResultData[i].PlanDepartmentID,
							label: aResultData[i].PlanDepartmentName
						})
					}
				} else if (res.data.status == 0 && res.data.count <= 0) {
					planDepartmentDataList.value = [];
				} else {
					planDepartmentDataList.value = [];
				}
			},
			fail: (error) => {
				uni.showToast({
					icon: 'none',
					title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
				});
				setTimeout(function() {
					uni.hideLoading();
				}, 5000);
			},
		})
	}

	const getSaleUserData = () => {
		console.log("--->>>销售员<<<---");
		uni.request({
			url: util.apiurl + 'rest/db/opensql',
			data: {
				token: getApp().globalData.Token,
				format: 'json',
				data: {
					db_name: getApp().globalData.AppDBName,
					sql_command_id: 'APP.BaseData.GetSaleUserData',
					params: []
				},
			},
			success: (res) => {
				if (res.data.status == 0 && res.data.count > 0) {
					console.log("---->" + JSON.stringify(res.data));
					var aResultData = res.data.data;
					for (var i = 0; i < aResultData.length; i++) {
						saleUserDataList.value.push({
							value: aResultData[i].SaleUserID,
							label: aResultData[i].SaleUserName
						})
					}
				} else {
					saleUserDataList.value = [];
				}
			},
			fail: (error) => {
				uni.showToast({
					icon: 'none',
					title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
				});
				setTimeout(function() {
					uni.hideLoading();
				}, 5000);
			},
		})
	}

	const getStoreStationName = () => {
		uni.request({
			url: util.apiurl + 'rest/db/opensql',
			data: {
				token: getApp().globalData.Token,
				format: 'json',
				data: {
					db_name: getApp().globalData.AppDBName,
					sql_command_id: 'APP.GetStoreStationName',
					params: [{
						name: 'No',
						value: storeStationNo.value
					}]
				},
			},
			success: (res) => {
				if (res.data.status == 0 && res.data.count > 0) {
					playSuccess();
					var aResultData = res.data.data;
					for (var i = 0; i < aResultData.length; i++) {
						storeStationName.value = aResultData[i].StoreStationName;
					};
					testFocus0.value = false;
					nextTick(() => {
						testFocus1.value = true;
						testFocus0.value = false;
					});
				} else {
					playError();
					storeStationName.value = "";
					testFocus0.value = false;
					nextTick(() => {
						testFocus1.value = false;
						testFocus0.value = true;
					});
					storeNameID.value = 0;
					billDataMessage.value = '不存在当前的仓位资料，请确认！';
				}
			},
			fail: (error) => {
				playError();
				billDataMessage.value = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
			},
		})
	}

	const getBillBusinessData = () => {
		uni.request({
			url: util.apiurl + 'rest/db/opensql',
			data: {
				token: getApp().globalData.Token,
				format: 'json',
				data: {
					db_name: getApp().globalData.AppDBName,
					sql_command_id: 'APP.GetBillBusinessList',
					params: [{
						name: 'TypeStatus',
						value: '2'
					}, {
						name: 'TypeNo',
						value: 'BusinessTypeStoreGoods'
					}]
				},
			},
			success: (res) => {
				if (res.data.status == 0 && res.data.count == 1) {
					var aResultData = res.data.data;
					for (var i = 0; i < aResultData.length; i++) {
						billBusinessID.value = aResultData[i].BillBusinessID;
						billTypeName.value = aResultData[i].BillTypeName;
						billBusinessDataList.value.push({
							value: aResultData[i].BillBusinessID,
							label: aResultData[i].BillTypeName
						})
					}
				} else if (res.data.status == 0 && res.data.count > 0) {
					console.log("--->>单据类型<<--" + JSON.stringify(res.data));
					var aResultData = res.data.data;
					for (var i = 0; i < aResultData.length; i++) {
						billBusinessDataList.value.push({
							value: aResultData[i].BillBusinessID,
							label: aResultData[i].BillTypeName
						})
					}
				} else if (res.data.status == 0 && res.data.count <= 0) {
					billBusinessDataList.value = [];
				} else {
					billBusinessDataList.value = [];
				}
			},
			fail: (error) => {
				uni.showToast({
					icon: 'none',
					title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
				});
				setTimeout(function() {
					uni.hideLoading();
				}, 5000);
			},
		})
	}

	const goodsOutBillDetailData = () => {
		uni.request({
			url: util.apiurl + 'rest/db/opensql',
			data: {
				token: getApp().globalData.Token,
				format: 'json',
				data: {
					db_name: getApp().globalData.AppDBName,
					sql_command_id: 'APP.GetStoreGoodsBusinessOutDetailDataSQL',
					params: [{
						name: 'BillID',
						value: outBillID.value
					}]
				},
			},
			success: (res) => {
				let data = res.data.data;
				console.log("---->" + JSON.stringify(res.data));
				if (res.data.status == 0 && res.data.count > 0) {
					var aResultData = res.data.data;
					outDetailList.value = res.data.data;
					commitType.value = aResultData[0].CommitType;
					commitProcName.value = aResultData[0].CommitProcName;
					fabricSumRoll.value = 0;
					fabricSumQty.value = 0;
					for (var i = 0; i < aResultData.length; i++) {
						if (parseFloat(aResultData[i].Roll) > 0) {
							fabricSumRoll.value = fabricSumRoll.value + aResultData[i].Roll;
						}

						if (parseFloat(aResultData[i].Qty) > 0) {
							fabricSumQty.value = fabricSumQty.value + aResultData[i].Qty;
						}
					};
					fabricSumRoll.value = fabricSumRoll.value.toFixed(2);
					fabricSumQty.value = fabricSumQty.value.toFixed(2);

				} else if (res.data.status == 0 && res.data.count <= 0) {
					outDetailList.value = [];
				} else {
					outDetailList.value = [];
				}
			},
		})
	}

	// 获取详细数据的点击事件处理
	const getDetailCrockNoListData = (item) => {
		console.log('点击详细数据:', item);
		// 这里可以添加点击详细数据的处理逻辑
	}

	// 日期修改
	const bindDateChange = (e) => {
		bjdDate.value = e.detail.value;
		bjdDateTime.value = new Date(e.detail.value + ' 00:00:00').getTime()
	}

	const scanFun = () => {
		uni.scanCode({
			success(res) {
				qrBarCode.value = res.result;
			}
		})
	}

	// 选择所属客户
	const selectCustomer = () => {
		let aTypeName = "客户";
		if (billTypeName.value == '成品销售出仓单') {
			aTypeName = "客户"
		} else if (billTypeName.value == '成品退染出仓单') {
			aTypeName = "染整厂"
		} else if (billTypeName.value == '销售调拨出仓单') {
			aTypeName = "客户"
		} else if (billTypeName.value == '内部调拨出仓单') {
			aTypeName = "客户"
		} else if (billTypeName.value == '成品采购退货单') {
			aTypeName = "成品供应商"
		} else if (billTypeName.value == '成品其它出仓单') {
			aTypeName = ""
		};

		uni.navigateTo({
			url: '../basedata/customer/customer?type=' + aTypeName + ''
		})
	}

	// 绑定客户
	const bjdKehuBindFun = (e) => {
		console.log("CustomerName===" + e.CustomerName);
		customerID.value = e.CustomerID;
		customerName.value = e.CustomerName;
		planDepartmentID.value = e.PlanDepartmentID;
		planDepartmentName.value = e.PlanDepartmentName;
		saleUserID.value = e.SaleUserID;
		saleUserName.value = e.SaleUserName;
		// saleCustomerAddress.value = e.CustomerAddress + ' ' + e.CustomerPhone + ' ' + e.CustomerLinkName;
	}

	// 提交按钮方法
	const submitBtnFun = () => {
		if (planDepartmentName.value == '') {
			playError();
			billDataMessage.value = '营销部门不能为空，请先输入营销部门！';
			return;
		}
		if (storeName.value == '') {
			playError();
			billDataMessage.value = '仓库名称不能为空，请先输入仓库名称！';
			return;
		}
		if (billTypeName.value == '') {
			playError();
			billDataMessage.value = '单据类型不能为空，请先输入单据类型！';
			return;
		}
		if (customerName.value == '') {
			playError();
			billDataMessage.value = '往来单位不能为空，请先输入往来单位！';
			return;
		}

		if (billTypeName.value == '销售调拨出仓单') {
			if (toStoreName.value == '') {
				playError();
				billDataMessage.value = '调至仓库不能为空，请先输入调至仓库！';
				return;
			}
		}

		if (billTypeName.value == '内部调拨出仓单') {
			if (toStoreName.value == '') {
				playError();
				billDataMessage.value = '调至仓库不能为空，请先输入调至仓库！';
				return;
			}
		}

		if (outBillID.value > 0) {
			playError();
			billDataMessage.value = '当前单据已经提交，不能重复提交！';
			return;
		}

		uni.request({
			url: util.apiurl + 'rest/db/storedproc',
			data: {
				token: getApp().globalData.Token,
				format: 'json',
				data: {
					db_name: getApp().globalData.AppDBName,
					proc_name: 'Usp_APP_StoreGoodsBusinessOutBillMaster',
					method: 'open_proc',
					params: [{
						name: '@BillMasterID',
						value: outBillID.value
					}, {
						name: '@BillBusinessID',
						value: billBusinessID.value
					}, {
						name: '@StoreNameID',
						value: storeNameID.value
					}, {
						name: '@ToStoreNameID',
						value: toStoreNameID.value
					}, {
						name: '@PlanDepartmentID',
						value: planDepartmentID.value
					}, {
						name: '@CustomerID',
						value: customerID.value
					}, {
						name: '@SaleUserID',
						value: saleUserID.value
					}, {
						name: '@Remark',
						value: remark.value
					}, {
						name: '@UserName',
						value: getApp().globalData.UserName
					}]
				},
			},
			success: (res) => {
				if (res.data.status == 0) {
					var aResultData = JSON.parse(res.data.data);
					if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
						playSuccess();
						outBillID.value = aResultData.GoodsOutBillID;
						outBillNo.value = aResultData.GoodsOutBillNo;
						goodsOutBillDetailData();
						billDataMessage.value = "";
						testFocus1.value = false;
						nextTick(() => {
							testFocus1.value = true;
						});
					} else {
						playError();
						billDataMessage.value = '提交出错！' + aResultData.BillDataMessage;
						return;
					}
				} else {
					playError();
					billDataMessage.value = '提交出错，' + res.data.msg
					return;
				}
			},
			fail: (error) => {
				playError();
				billDataMessage.value = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
			},
		})
	}

	// 最重要的扫描方法
	const goodsOutBillDetailScan = () => {
				if (this.StoreNameID == 0 && this.OutBillNo == '') {
					this.playError();
					this.BillDataMessage = '请先新增单据';
					return;
				}
				console.log("this.QRBarCode ---->>" + this.QRBarCode);

				let aBarCodeDelStatus = 0;
				if (this.BarCodeDelStatus){
					aBarCodeDelStatus = '1';
				};

				let aAllCrockNoScanStatus = 0;
				if (this.AllCrockNoScanStatus){
					aAllCrockNoScanStatus = '1';
				};

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreGoodsBusinessOutBillDetail',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.OutBillID
								},
								{
									name: '@QRBarCode',
									value: this.QRBarCode
								},
								{
									name: '@BarCodeDelStatus',
									value: aBarCodeDelStatus
								},
								{
									name: '@AllCrockNoScanStatus',
									value: aAllCrockNoScanStatus
								},
								{
									name: '@StoreStationName',
									value: this.StoreStationName
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.FabricGoodsNo = aResultData.FabricGoodsNo;
								this.FabricGoodsName = aResultData.FabricGoodsName;
								this.GoodsCodeNo = aResultData.GoodsCodeNo;
								this.GoodsCodeName = aResultData.GoodsCodeName;
								this.CrockNo = aResultData.CrockNo;
								this.GoodsBillNo = aResultData.GoodsBillNo;
								this.GoodsQty = aResultData.GoodsQty;
								this.BillSumRoll = parseFloat(aResultData.BillSumRoll);
								this.BillSumQty = parseFloat(aResultData.BillSumQty);
								this.GoodsCodeSumRoll = parseFloat(aResultData.GoodsCodeSumRoll);
								this.GoodsCodeSumQty = parseFloat(aResultData.GoodsCodeSumQty);
								this.CrockNoSumRoll = parseFloat(aResultData.CrockNoSumRoll);
								this.CrockNoSumQty = parseFloat(aResultData.CrockNoSumQty);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.GoodsOutBillDetailData();
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '出仓扫描出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '出仓扫描出错，' + res.data.msg;
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.testFocus1 = false;
						this.$nextTick(() => {
							this.testFocus1 = true;
							this.testFocus0 = false;
						});
						this.BillDataMessage = '出仓扫描出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			// 审核按钮方法
			commitBtnFun: function() {
				if (this.OutBillID <= 0) {
					this.playError();
					this.BillDataMessage = '当前单据未提交，不能审核或消审！';
					return;
				}

				var aCommitRecallName = '审核';
				if (this.commitType == ''){
					aCommitRecallName = '审核';
				} else {
					aCommitRecallName = '消审';
				}

				if (this.CommitProcName == ''){
					this.playError();
					this.BillDataMessage = '当前'+ aCommitRecallName +'操作配置有误，不能审核！';
					return;
				}

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: this.CommitProcName,
							method: 'open_proc',
							params: [{
								name: '@BillID',
								value: this.OutBillID
							}, {
								name: '@BillNo',
								value: this.OutBillNo
							}, {
								name: '@UserName',
								value: getApp().globalData.UserName
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.BillDataMessage = aCommitRecallName + "成功！";

								if (aCommitRecallName == '审核'){
									this.commitType = '已审核'
								} else {
									this.commitType = ''
								}
							} else {
								this.playError();
								this.BillDataMessage = aCommitRecallName + '出错！' + aResultData.BillDataMessage;
								return;
							}
						} else {
							this.playError();
							this.BillDataMessage = aCommitRecallName + '出错，' + res.data.msg
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			})
}
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
		padding-bottom: 160rpx;
	}

	.page-container {
		min-height: 100vh;
		background-color: #F5F5F5;
	}

	/* 条码扫描区域 */
	.scan-section {
		background-color: #FFFFFF;
		margin: 20rpx;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.scan-header {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.scan-icon {
		margin-right: 16rpx;
	}

	.barcode-icon {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.scan-text {
		flex: 1;
		font-size: 32rpx;
		color: #333333;
	}

	.scan-options {
		display: flex;
		align-items: center;
	}

	.radio-group {
		display: flex;
		gap: 32rpx;
	}

	.radio-item {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.radio-circle {
		width: 32rpx;
		height: 32rpx;
		border: 2rpx solid #CCCCCC;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;

		&.active {
			border-color: #007AFF;
		}
	}

	.radio-dot {
		width: 16rpx;
		height: 16rpx;
		background-color: #007AFF;
		border-radius: 50%;
	}

	.radio-label {
		font-size: 28rpx;
		color: #333333;
	}

	.scan-input-container {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.scan-input {
		flex: 1;
		height: 80rpx;
		background-color: #F8F8F8;
		border-radius: 8rpx;
		padding: 0 24rpx;
		font-size: 28rpx;
		border: none;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #FFFFFF;
		padding: 20rpx 32rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		font-weight: 500;
	}

	/* 基础信息区域的样式现在由 expandable-form 组件处理 */

	/* 出仓细码区域 */
	.detail-section {
		background-color: #FFFFFF;
		margin: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.detail-header {
		padding: 32rpx;
		border-bottom: 1rpx solid #F0F0F0;
	}

	.detail-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 8rpx;
	}
	.detail-summary-container {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.detail-summary {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 8rpx;
	}

	.detail-count {
		font-size: 24rpx;
		color: #666666;
	}

	.detail-list {
		padding: 0 32rpx 32rpx;
	}

	.detail-item {
		background-color: #F8F8F8;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 16rpx;
		position: relative;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.detail-item-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16rpx;
	}

	.detail-code {
		font-size: 28rpx;
		font-weight: 600;
		color: #333333;
	}

	.detail-arrow {
		font-size: 24rpx;
		color: #CCCCCC;
	}

	.detail-item-content {
		margin-bottom: 16rpx;
	}

	.detail-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 8rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.detail-label {
		font-size: 24rpx;
		color: #666666;
	}

	.detail-tag {
		position: absolute;
		bottom: 24rpx;
		right: 24rpx;
		background-color: #007AFF;
		border-radius: 20rpx;
		padding: 8rpx 16rpx;
	}

	.tag-text {
		font-size: 20rpx;
		color: #FFFFFF;
		font-weight: 500;
	}

	/* 底部按钮 */
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		padding: 24rpx 40rpx;
		border-top: 1rpx solid #F0F0F0;
		display: flex;
		gap: 24rpx;
		z-index: 100;
	}

	.action-btn {
		flex: 1;
		height: 88rpx;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.save-btn {
		background-color: #FFFFFF;
		border: 2rpx solid #007AFF;
	}

	.save-btn .btn-text {
		color: #007AFF;
		font-size: 32rpx;
		font-weight: 500;
	}

	.submit-btn {
		background-color: #007AFF;
	}

	.submit-btn .btn-text {
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: 500;
	}

	/* 消息显示 */
	.message-display {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba(0, 0, 0, 0.8);
		color: #FFFFFF;
		padding: 24rpx 32rpx;
		border-radius: 8rpx;
		z-index: 1000;
	}

	.message-text {
		font-size: 28rpx;
	}
</style>
