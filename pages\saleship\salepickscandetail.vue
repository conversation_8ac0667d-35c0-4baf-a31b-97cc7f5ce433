<template>
  <view class="wrap">
    <u-form ref="uForm">
      <u-form-item>
        <text class="title" style="width: 200px">单号：{{ billNo }}</text>
        <text class="title" style="width: 200px">日期：{{ billDate }}</text>
      </u-form-item>
      <u-form-item>
        <text class="title" style="width: 200px"
          >客户名称：{{ customerName }}</text
        >
        <text class="title" style="width: 200px"
          >销 售 员：{{ saleUserName }}</text
        >
      </u-form-item>
      <u-form-item>
        <text class="title" style="width: 200px"
          >仓库名称：{{ storeName }}{{ toStoreName }}</text
        >
      </u-form-item>
      <u-form-item>
        <text class="title" style="width: 200px"
          >备注内容：{{ billRemark }}</text
        >
      </u-form-item>
      <u-form-item label-width="130" label="条码资料:">
        <input
          type="text"
          v-model="qrBarCode"
          maxlength="-1"
          style="width: 170px"
          @confirm="salePickBillDetailScan"
        />
        <checkbox-group @change="handleAllCrockNoChange">
          <checkbox :checked="allCrockNoScanStatus">整缸</checkbox>
        </checkbox-group>
        <checkbox-group @change="handleBarCodeDelChange">
          <checkbox :checked="barCodeDelStatus">删除</checkbox>
        </checkbox-group>
      </u-form-item>
    </u-form>
    <u-form ref="uForm">
      <u-form-item>
        <text class="title" style="width: 400px"
          >成品名称：{{ fabricGoodsNo }}{{ fabricGoodsName }}</text
        >
      </u-form-item>
      <u-form-item>
        <text class="title" style="width: 400px"
          >色号颜色：{{ goodsCodeNo }}{{ goodsCodeName }}</text
        >
      </u-form-item>
      <u-form-item>
        <text class="title" style="width: 200px">成品缸号：{{ crockNo }}</text>
        <text class="title" style="width: 200px"
          >成品卷号：{{ goodsBillNo }}</text
        >
      </u-form-item>
      <u-form-item>
        <text class="title">{{ billDataMessage }}</text>
      </u-form-item>
      <u-form-item>
        <text class="title" style="width: 130px"
          >配布条数：{{ billSumRoll }}</text
        >
        <text class="title" style="width: 130px">数量：{{ billSumQty }}</text>
        <text class="title" style="width: 120px">米数：{{ billSumMQty }}</text>
      </u-form-item>
      <u-form-item>
        <text class="title" style="width: 130px"
          >已配条数：{{ billScanRoll }}</text
        >
        <text class="title" style="width: 130px">数量：{{ billScanQty }}</text>
        <text class="title" style="width: 120px">米数：{{ billScanMQty }}</text>
      </u-form-item>
      <view class="submitView">
        <u-button
          type="primary"
          class="submitBtn"
          :ripple="true"
		  :loading="submitLoading"
          ripple-bg-color="#909399"
          @click="submitBtnFun"
        >
          {{ pageType ? "保存" : "提交" }}
        </u-button>
      </view>
    </u-form>
    <view class="u-demo-area">
      <u-toast ref="uToast"></u-toast>
      <wyb-table
        ref="table"
        :headers="headersMaster"
        :contents="goodsDetailList"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { onLoad, onShow, onHide } from '@dcloudio/uni-app'
import { formatDate } from '@/common/tool.js'
import util from "@/common/util"

// 响应式数据
const saleBillNo = ref("") // 单号
const billMasterID = ref(0) // 订单ID
const borderColor = ref("#e4e7ed")
const align = ref("center")
const index = ref(0)
const pageType = ref("")
const submitLoading = ref(false)
const actionSheetShow = ref(false)
const qrBarCode = ref("")
const billNo = ref("")
const billDate = ref("")
const billTypeID = ref(0)
const billRemark = ref("")
const customerName = ref("")
const storeNameID = ref(0)
const storeName = ref("")
const toStoreName = ref("")
const saleUserName = ref("")
const billTypeName = ref("")
const fabricGoodsNo = ref("")
const fabricGoodsName = ref("")
const goodsCodeNo = ref("")
const goodsCodeName = ref("")
const crockNo = ref("")
const goodsBillNo = ref("")
const billSumRoll = ref(0)
const billSumQty = ref(0)
const billSumMQty = ref(0)
const billScanRoll = ref(0)
const billScanQty = ref(0)
const billScanMQty = ref(0)
const allCrockNoScanStatus = ref(false)
const barCodeDelStatus = ref(false)
const goodsDetailList = ref([])
const billDataMessage = ref("")

const headersMaster = ref([
  {
    label: "成品编号",
    key: "product_code",
  },
  {
    label: "色号",
    key: "product_color_code",
  },
  {
    label: "颜色",
    key: "product_color_name",
  },
  {
    label: "缸号",
    key: "dye_factory_dyelot_number",
  },
  {
    label: "配布条数",
    key: "push_roll",
  },
  {
    label: "配布数量",
    key: "push_weight",
  },
  {
    label: "配布长度",
    key: "push_length",
  },
  {
    label: "已配条数",
    key: "arrange_roll",
  },
  {
    label: "已配数量",
    key: "arrange_weight",
  },
  {
    label: "成品等级",
    key: "product_level_name",
  },
  {
    label: "成品备注",
    key: "product_remark",
  },
])

const scanningInput = ref("") // 用于累积扫码输入
const lastKeyTime = ref(0) // 用于判断扫码速度
const scanReceiver = ref(null)
const isPageActive = ref(false) // 添加页面活动状态标志

// 生命周期钩子
onLoad((e) => {
  if (e.billid) {
    billMasterID.value = e.billid;
  }
  if (e.order_no) {
    saleBillNo.value = e.order_no;
  }
  salePickDetail();

  // #ifdef APP-PLUS
  isPageActive.value = true;
  registerScanBroadcast();
  // #endif
})

onUnmounted(() => {
  // #ifdef APP-PLUS
  isPageActive.value = false;
  // unregisterBroadcast();
  // #endif
})

onHide(() => {
  // 页面隐藏时
  isPageActive.value = false;
})

onShow(() => {
  // 页面显示时
  isPageActive.value = true;
})

// 方法定义
const playSuccess = () => {
  util.playSuccessAudio();
}

const playError = () => {
  util.playErrorAudio();
}

const handleAllCrockNoChange = () => {
  if (allCrockNoScanStatus.value) {
    allCrockNoScanStatus.value = false;
  } else {
    allCrockNoScanStatus.value = true;
    barCodeDelStatus.value = false;
  }
}

const handleBarCodeDelChange = () => {
  if (barCodeDelStatus.value) {
    barCodeDelStatus.value = false;
  } else {
    barCodeDelStatus.value = true;
    allCrockNoScanStatus.value = false;
  }
}

const salePickDetail = () => {
  // 获取成品配布单详情
  uni.$u.api.getFpmArrangeOrder({
    id: billMasterID.value,
    order_no: saleBillNo.value
  }).then(res => {
    var aResultData = res;

    billMasterID.value = aResultData.id; // 重写订单ID
    billNo.value = aResultData.order_no; // 重写单号
    billDate.value = formatDate(aResultData.arrange_time, "Y-M-D");
    customerName.value = aResultData.biz_unit_name;
    storeNameID.value = aResultData.warehouse_id;
    storeName.value = aResultData.warehouse_name;
    saleUserName.value = aResultData.sale_user_name;
    billTypeName.value = aResultData.out_order_type_name;
    billRemark.value = aResultData.internal_remark;
    billSumRoll.value = aResultData.push_roll / 100 + "匹";
    billSumQty.value = aResultData.push_weight / 10000 + "Kg";
    billSumMQty.value = aResultData.push_length / 10000 + "米";
    billScanRoll.value = aResultData.total_roll / 100 + "匹";
    billScanQty.value = aResultData.total_weight / 10000 + "Kg";
    billScanMQty.value = aResultData.total_length / 10000 + "米";

    let aResultDataList = res.item_data;
    for (var i = 0; i < aResultDataList.length; i++) {
      aResultDataList[i].push_roll = aResultDataList[i].push_roll / 100;
      aResultDataList[i].push_weight = aResultDataList[i].push_weight / 10000;
      aResultDataList[i].push_length = aResultDataList[i].push_length / 10000;
      aResultDataList[i].arrange_roll = aResultDataList[i].arrange_roll / 100;
      aResultDataList[i].arrange_weight = aResultDataList[i].arrange_weight / 10000;
    }

    goodsDetailList.value = aResultDataList;

  }).catch(error => {
    showError(error.msg);
  });
}

// 添加通用错误提示方法
const showError = (message) => {
  playError();
  uni.showModal({
    title: '提示',
    content: message,
    showCancel: false
  });
}

// 添加成功提示方法
const showSuccess = (message) => {
  playSuccess();
  uni.showToast({
    title: message,
    icon: 'success'
  });
}

const handleScans = () => {
  // 删除模式判断
  let aBarCodeDelStatus = 1; // 1=正常扫码，3=删除模式
  if (barCodeDelStatus.value) {
    aBarCodeDelStatus = 3;
  }

  // 整缸扫描判断
  let aAllCrockNoScanStatus = 2; // 0=普通扫描，1=整缸扫描
  if (allCrockNoScanStatus.value) {
    aAllCrockNoScanStatus = "1";
  }

  // 初始化条码和二维码变量
  let aQRBarCode = "";
  let aBarCode = "";

  // 详细的调试信息
  console.log('扫码原始值:', qrBarCode.value);
  console.log('扫码值长度:', qrBarCode.value.length);
  console.log('扫码值前10个字符:', qrBarCode.value.substring(0, 10));
  // 检查是否有不可见字符
  console.log('扫码值ASCII码:', Array.from(qrBarCode.value).map(char => char.charCodeAt(0)));

  // 清理可能的空格和不可见字符
  const cleanedCode = qrBarCode.value.trim();

  if (cleanedCode.startsWith("66^")) {
    aQRBarCode = cleanedCode.replace(/\s+/g, '');
  } else {
    aBarCode = cleanedCode.replace(/\s+/g, '');
  }
  console.log('请求参数', aBarCodeDelStatus);
  console.log('请求参数', aBarCode);
  console.log('请求参数', aQRBarCode);
  console.log('请求参数', billMasterID.value);
  console.log('请求参数 SaleBillNo', saleBillNo.value);
  console.log('请求参数 token', uni.getStorageSync("userToken").Token);

  // 发送请求
  util.request({
    url: "/product/fpmArrangeOrder/updateFpmArrangeOrder",
    method: "PUT",
    header: {
      Platform: 2,
      Authorization: uni.getStorageSync("userToken").Token,
    },
    data: {
      arrange_type: aBarCodeDelStatus, // 1=正常扫码，3=删除模式
      bar_code: aBarCode, // 条码（如果是条形码就使用这个）
      qr_code: aQRBarCode, // 二维码（如果是二维码就使用这个）
      id: parseInt(billMasterID.value), // 订单ID
      // order_no: saleBillNo.value, // 单号
    },
    success: (res) => {
      console.log('API响应:', res);

      if (res.data.code == 0 && res.data.msg == "success") {
        var aResultData = res.data.data;
        playSuccess();
        fabricGoodsNo.value = aResultData.product_code;
        fabricGoodsName.value = aResultData.product_name;
        goodsCodeNo.value = aResultData.product_color_code;
        goodsCodeName.value = aResultData.product_color_name;
        crockNo.value = aResultData.dyelot_number;
        goodsBillNo.value = aResultData.volume_number;
        qrBarCode.value = "";

        if (aBarCodeDelStatus == 1) {
          showSuccess("扫描成功！");
        } else {
          showSuccess("删除成功！");
        }

        salePickDetail();
      } else {
        showError("扫描出错，" + res.data.msg);
        qrBarCode.value = "";
      }
    },
    fail: (error) => {
      console.error('API请求失败:', error);
      qrBarCode.value = "";
      showError("连接服务器出错，请检查后台服务是否启动！");
    },
  });
}

// 注册扫码广播接收器
const registerScanBroadcast = () => {
      try {
        const main = plus.android.runtimeMainActivity();

        // 先配置扫码枪广播设置
        try {
          const Intent = plus.android.importClass("android.content.Intent");
          const intent = new Intent("com.android.scanner.service_settings");
          intent.putExtra(
            "action_barcode_broadcast",
            "com.android.server.scannerservice.broadcast"
          );
          intent.putExtra("key_barcode_broadcast", "scannerdata");
          main.sendBroadcast(intent);
        } catch (error) {
          console.error("配置扫码枪广播失败：", error);
        }

        // 注册广播接收器
        const IntentFilter = plus.android.importClass(
          "android.content.IntentFilter"
        );
        const filter = new IntentFilter();
        filter.addAction("com.android.server.scannerservice.broadcast");
        console.log("添加广播action完成");

        const receiver = plus.android.implements(
          "io.dcloud.feature.internal.reflect.BroadcastReceiver",
          {
            onReceive: (context, intent) => {
              // 只有当页面活动时才处理广播
              if (!isPageActive.value) return;

              try {
                const scanResult = intent.getStringExtra("scannerdata");
                console.log("配布单详情-扫码结果:", scanResult);
                if (scanResult) {
                  qrBarCode.value = scanResult;
                  nextTick(() => {
                    handleScans();
                  });
                }
              } catch (error) {
                console.error("处理广播数据时出错：", error);
              }
            },
          }
        );

        // 注册广播接收器
        main.registerReceiver(receiver, filter);
        scanReceiver.value = receiver;
        console.log("扫码广播注册成功，等待扫码...");
      } catch (error) {
        console.error("注册扫码广播失败：", error);
        console.error("错误详情：", error.message);
        console.error("错误堆栈：", error.stack);
      }
}

// 注销扫码广播接收器
const unregisterScanBroadcast = () => {
  if (scanReceiver.value) {
    try {
      const main = plus.android.runtimeMainActivity();
      main.unregisterReceiver(scanReceiver.value);
      scanReceiver.value = null;
      console.log("扫码广播注销成功");
    } catch (error) {
      console.error("注销扫码广播失败：", error);
    }
  }
}

const salePickBillDetailScan = () => {
  // 聚焦输入框触发的方法
  handleScans();
}

// 提交按钮方法
const submitBtnFun = () => {
  if (billMasterID.value == 0) {
    playError();
    billDataMessage.value = "单号为空，不能提交";
    return;
  }
  // 获取配布总匹数和已配匹数进行比较
  const totalRoll = parseFloat(billSumRoll.value.replace('匹', '')); // 配布总匹数
  const arrangedRoll = parseFloat(billScanRoll.value.replace('匹', '')); // 已配匹数

  if (arrangedRoll < totalRoll) {
    // 如果已配匹数小于总匹数,显示确认弹窗
    uni.showModal({
      title: '提示',
      content: '配布匹数不足，是否确定提交',
      cancelText: '取消',
      confirmText: '确定',
      success: (res) => {
        if (res.confirm) {
          // 用户点击确定,继续提交
          submitOrder();
        }
      }
    });
  } else {
    // 匹数足够,直接提交
    submitOrder();
  }
}

// 新增提交订单的方法
const submitOrder = () => {
  submitLoading.value = true
  uni.$u.api.outFpmArrangeOrder({
    id: parseInt(billMasterID.value)
  }).then(res => {
    submitLoading.value = false
    console.log('outFpmArrangeOrder res',res)
    if (res) {
      billDataMessage.value = "成功生成出仓单！";
      playSuccess();
    } else {
      playError();
      billDataMessage.value = "提交生成出仓单出错！" + res.msg;
      return;
    }
  }).catch(error => {
    submitLoading.value = false
    playError();
    billDataMessage.value = "连接服务器出错，请检查后台服务是否启动！";
  });
}
</script>

<style>
page {
  background-color: #f8f8f8;
  padding-bottom: 260rpx;
}

.u-radio {
  width: 200rpx !important;
}

.submitView {
  width: 100%;
  padding: 16rpx 0 26rpx;
  background-color: #ffffff;
  position: fixed;
  bottom: 0;
  left: 0;
  border-top: 1rpx solid #f1f1f1;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.submitBtn {
  width: 666rpx;
}

.productBox {
  background-color: #ffffff;
  margin-top: 32rpx;
  padding: 26rpx 0;
}

.tjcpName {
  width: 686rpx;
  height: 40rpx;
  font-size: 16px;
  font-weight: bold;
  border-left: 6rpx solid #007aff;
  padding-left: 12rpx;
  margin-left: 26rpx;
  margin-top: 26rpx;
}

.cpInput {
  width: 150rpx !important;
  margin-right: 12rpx;
}

.cpInput > input {
  box-sizing: border-box;
  border: 1rpx solid #dddddd;
  width: 100%;
  height: 60rpx;
  border-radius: 10rpx;
  padding: 0 10rpx;
}

.cpInput1 {
  width: 200rpx !important;
  margin-right: 12rpx;
}

.cpInput1 > input {
  box-sizing: border-box;
  border: 1rpx solid #dddddd;
  width: 100%;
  height: 60rpx;
  border-radius: 10rpx;
  padding: 0 10rpx;
}

.clearIcon {
  position: absolute;
  right: 6rpx;
  top: 6rpx;
}

.greenPrice {
  font-size: 16px;
  color: #19be6b !important;
  font-weight: bold;
}

.disFlex {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.inputName {
  color: #adadad;
  font-size: 16px;
}

.addHKQS {
  display: flex;
  align-items: center;
  padding: 16rpx 26rpx;
  font-size: 15px;
  font-weight: bold;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
