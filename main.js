/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
import { createSSRApp } from 'vue';
import App from './App';

// 全局存储 vuex 的封装
import store from '@/store';

// 引入全局 uView Plus 框架
import uviewPlus from 'uview-plus';

// Vue i18n 国际化
import { createI18n } from 'vue-i18n';

// i18n 部分的配置，引入语言包，注意路径
import lang_zh_CN from '@/common/locales/zh_CN.js';
import lang_en from '@/common/locales/en.js';

// http 拦截器 - 使用uView-Plus优化版本
import httpInterceptor from '@/common/http.interceptor.js';

// http 接口 API 抽离
import httpApi from '@/common/http.api.js';

// 工具函数
import { replaceAll } from '@/common/common.js';

export function createApp() {
	const app = createSSRApp(App);

	// 使用 store
	app.use(store);

	// 使用 uView Plus UI 框架
	app.use(uviewPlus, () => {
		return {
			options: {
				// 修改$u.config对象的属性
				config: {
					// 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
					unit: 'rpx'
				}
			}
		}
	});

	// 配置 i18n
	const i18n = createI18n({
		legacy: true, // 使用 Legacy API 模式以支持 $t 函数
		locale: uni.getLocale() || 'zh_CN', // 获取已设置的语言，默认中文
		fallbackLocale: 'zh_CN', // 回退语言改为中文
		messages: {
			'zh_CN': lang_zh_CN,
			'en': lang_en,
		},
		// 添加全局属性配置
		globalInjection: true
	});
	app.use(i18n);

	// 全局属性
	app.config.globalProperties.replaceAll = replaceAll;

	// 确保 uView-Plus 时间工具方法可用
	if (!app.config.globalProperties.$u.timeFormat) {
		// 手动添加 timeFormat 方法
		app.config.globalProperties.$u.timeFormat = (timestamp, format = 'yyyy-mm-dd') => {
			if (!timestamp) return '';

			// 处理时间戳
			let date;
			if (typeof timestamp === 'string') {
				// 如果是字符串，尝试解析
				if (timestamp.includes('-') || timestamp.includes('/')) {
					date = new Date(timestamp);
				} else {
					// 如果是纯数字字符串，当作时间戳处理
					const ts = parseInt(timestamp);
					date = new Date(ts < 10000000000 ? ts * 1000 : ts);
				}
			} else if (typeof timestamp === 'number') {
				// 如果是数字，判断是秒还是毫秒
				date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
			} else {
				date = new Date(timestamp);
			}

			// 检查日期是否有效
			if (isNaN(date.getTime())) {
				console.warn('Invalid date:', timestamp);
				return '';
			}

			// 格式化
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');

			return format
				.replace(/yyyy/g, year)
				.replace(/mm/g, month)
				.replace(/dd/g, day)
				.replace(/hh/g, hours)
				.replace(/MM/g, minutes)
				.replace(/ss/g, seconds);
		};

		console.log('✅ 手动添加 timeFormat 方法');
	}

	// 使用 http 拦截器
	app.use(httpInterceptor);

	// 使用 http API
	app.use(httpApi);

	// 引入 uView Plus 提供的对 vuex 的简写法文件
	try {
		let vuexStore = require('@/store/$u.mixin.js');
		if (vuexStore && vuexStore.default) {
			app.mixin(vuexStore.default);
		}
	} catch (error) {
		console.warn('vuex mixin not found:', error);
	}

	// 引入 uView Plus 对小程序分享的 mixin 封装
	try {
		let mpShare = require('uview-plus/libs/mixin/mpShare.js');
		if (mpShare && mpShare.default) {
			app.mixin(mpShare.default);
		}
	} catch (error) {
		console.warn('mpShare mixin not found:', error);
	}

	return { app };
}
