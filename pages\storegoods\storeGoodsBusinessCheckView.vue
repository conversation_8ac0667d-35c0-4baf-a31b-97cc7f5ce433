<template>
	<view class="myCard" >
		<view class="cardTopName">盘点单号：{{props.item.order_no}}</view>
		<view class="cardRow">
			<view>盘点日期：</view>
			<view>{{formatDate(props.item.check_time, 'Y-M-D')}}</view>
		</view>
		<view class="cardRow">
			<view>仓库名称：</view>
			<view>{{props.item.warehouse_name}}</view>
		</view>
		<view class="cardRow">
			<view>仓位名称：</view>
			<view>{{props.item.warehouse_bin_name}}</view>
		</view>
		<view class="cardRow">
			<view>创 建 人：</view>
			<view>{{props.item.create_user_name}} {{formatDate(props.item.create_time, 'Y-M-D h:m:s')}}</view>
		</view>
		<view class="cardRow">
			<view>审 核 人：</view>
			<view>{{props.item.auditor_name}} {{formatDate(props.item.audit_date, 'Y-M-D h:m:s')}}</view>
		</view>
		<view v-if="!props.isDetail && !props.isSelect" class="lookDetail">
			<text>查看详情</text>
			<up-icon name="arrow-right" size="20"></up-icon>
		</view>
	</view>
</template>

<script setup>
	import { formatDate } from '@/common/tool.js'

	// Props 定义
	const props = defineProps({
		item: {
			type: Object,
			default: () => ({})
		},
		index: {
			type: Number,
			default: 0
		},
		isSelect: {
			type: Boolean,
			default: false
		},
		isDetail: {
			type: Boolean,
			default: false
		}
	})
</script>

<style>
	
</style>

