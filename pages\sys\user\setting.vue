<template>
	<view class="wrap">
		<up-gap height="20" bg-color="#f5f5f5"></up-gap>
		<up-cell-group :border="false">
			<up-cell title="通用" @click="navTo('/pages/sys/user/currency')" :arrow="true"></up-cell>
			<up-gap height="20" bg-color="#f5f5f5"></up-gap>
			<up-cell title="版本更新"   @click="upgrade()" :arrow="true"></up-cell>
			<up-cell title="隐私政策" @click="navTo('')" :arrow="true"></up-cell>
			<up-cell title="用户协议" @click="navTo('')" :arrow="true"></up-cell>
		</up-cell-group>
		<up-gap height="20" bg-color="#f5f5f5"></up-gap>
		<view>
			<u-button class="sign-out"  @click="logout" :hair-line="false">退出登录</u-button>
		</view>
	</view>
</template>
<script>
/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
import app_upgrade from '@/uni_modules/app-upgrade/js_sdk/index.js'

export default {
	data() {
		return {
			
		};
	},
	methods: {
		navTo(url) {
			uni.navigateTo({
				url: url
			});
		},
		upgrade(){
		      // #ifdef APP-PLUS
		      // this.$u.api.upgradeCheck().then(res => {
		      // 	if (res.result == 'true'){
		      // 		uni.showModal({
		      // 			title: '提示',
		      // 			content: res.message + '是否下载更新？',
		      // 			showCancel: true,
		      // 			success: function (res2) {
		      // 				if (res2.confirm) {
		      // 					plus.runtime.openURL('http://aidex.vip/');
		      // 				}
		      // 			}
		      // 		});
		      // 	}else{
		      // 		this.$u.toast(res.message);
		      // 	}
		      // });
		      app_upgrade(async (versionCode)=>{
		        // 查询是否更新
		        //         const { statusCode,data }=await uni.request({
		        //             url:'https://xxx',
		        //             data:{
		        //                 'versionCode':versionCode
		        //             },
		        //             method:'POST'
		        //         })
		
		        //         if(statusCode==200){
		        //             return {
		        //                 changelog: data.changelog,
		        //                 status: data.status, // 0 无新版本 | 1 有新版本
		        //                 path: data.path // 新apk地址
		        //             }
		        //         }
		        return {
		          changelog: '修改已知的bug',
		          status: 1, // 0 无新版本 | 1 有新版本
		          path: 'https://testhcscm.cdn.zzfzyc.com/android_debug/android_debug.apk' // 新apk地址
		        }
		      }, 1)
		      // #endif
		      // #ifndef APP-PLUS
		      this.$u.toast('小程序端或H5端已是最新版，无需检查更新！');
		      // #endif
		    },
		openSettings() {
			// #ifdef APP-PLUS
			uni.getSystemInfo({  
				success(res) {  
					if(res.platform == 'ios'){
						plus.runtime.openURL("app-settings://");
					} else if (res.platform == 'android'){
						var main = plus.android.runtimeMainActivity();  
						var Intent = plus.android.importClass("android.content.Intent");
						var mIntent = new Intent('android.settings.SOUND_SETTINGS');
						main.startActivity(mIntent);
					}
				}
			});
			// #endif
			// #ifndef APP-PLUS
			this.$u.toast('小程序端或H5端已是最新版，无需检查更新！');
			// #endif
		},
		logout() {
			this.$u.api.pdaLogout().then(res => {
				console.log('res',res)
				if (res.code != 0) {
					// this.$u.toast('退出登录失败');
					uni.reLaunch({
						url: '/pages/sys/login/index'
					});
					return
				}
				// 清除token和用户信息
				uni.removeStorageSync('token');
				this.$store.dispatch('updateUserInfo', null);
				uni.reLaunch({
					url: '/pages/sys/login/index'
				});
			}).catch(error => {
				console.log('error',error)
				// 即使退出登录失败，也要清除本地token和用户信息
				uni.removeStorageSync('token');
				this.$store.dispatch('updateUserInfo', null);
				// this.$u.toast('退出登录失败');
			});
		}
	}
};
</script>
<style lang="scss">

.wrap{
	background-color: #fff;
}
page {
	background-color: #f5f5f5;
}

::v-deep( .up-cell-title) {
	padding: 25rpx 30rpx;
	font-size: 30rpx;
}
</style>
